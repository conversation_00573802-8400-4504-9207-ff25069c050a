"use client"

import { useState, useEffect } from "react"
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Progress } from "@/components/ui/progress"
import { MapPin } from "lucide-react"
import { CurrencyDisplay } from "@/components/ui/currency-display"
import { useLocations } from "@/lib/location-provider"

export function LocationPerformance() {
  const { locations, isHomeServiceEnabled } = useLocations()
  const [locationData, setLocationData] = useState<Array<{
    id: string;
    name: string;
    revenue: number;
    appointments: number;
    utilization: number;
    color: string;
  }>>([])

  // Colors for different locations
  const colors = [
    "bg-blue-100 text-blue-800",
    "bg-green-100 text-green-800",
    "bg-purple-100 text-purple-800",
    "bg-orange-100 text-orange-800",
    "bg-pink-100 text-pink-800",
    "bg-yellow-100 text-yellow-800",
    "bg-indigo-100 text-indigo-800",
  ]

  // Update location data when locations change
  useEffect(() => {
    // Generate performance data for each location
    const activeLocations = locations.filter(loc => loc.status === "Active")

    const newLocationData = activeLocations.map((location, index) => {
      // Generate some random but consistent data for each location
      const revenue = 5000 + (location.id.charCodeAt(0) * 100) + Math.floor(Math.random() * 5000)
      const appointments = 50 + (location.id.charCodeAt(0) % 10) * 10 + Math.floor(Math.random() * 50)
      const utilization = 60 + (location.id.charCodeAt(0) % 10) * 3 + Math.floor(Math.random() * 20)

      return {
        id: location.id,
        name: location.name,
        revenue,
        appointments,
        utilization: Math.min(utilization, 100), // Cap at 100%
        color: colors[index % colors.length],
      }
    })

    // Add home service if enabled
    if (isHomeServiceEnabled) {
      newLocationData.push({
        id: "home",
        name: "Home Service",
        revenue: 3500,
        appointments: 42,
        utilization: 65,
        color: "bg-orange-100 text-orange-800",
      })
    }

    setLocationData(newLocationData)
  }, [locations, isHomeServiceEnabled])

  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-xl font-bold">Location Performance</CardTitle>
        <p className="text-sm text-muted-foreground">Performance metrics across all salon locations.</p>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="revenue">
          <TabsList className="mb-4">
            <TabsTrigger value="revenue">Revenue</TabsTrigger>
            <TabsTrigger value="appointments">Appointments</TabsTrigger>
            <TabsTrigger value="utilization">Utilization</TabsTrigger>
          </TabsList>

          <TabsContent value="revenue">
            <div className="space-y-6">
              {locationData.map((location) => (
                <div key={location.id} className="space-y-2">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <div className={`h-8 w-8 rounded-full ${location.color} flex items-center justify-center`}>
                        <MapPin className="h-4 w-4" />
                      </div>
                      <p className="font-medium">{location.name}</p>
                    </div>
                    <div className="text-right">
                      <p className="font-medium"><CurrencyDisplay amount={location.revenue} /></p>
                    </div>
                  </div>
                  <Progress value={(location.revenue / 15000) * 100} className="h-2" />
                </div>
              ))}
            </div>
          </TabsContent>

          <TabsContent value="appointments">
            <div className="space-y-6">
              {locationData.map((location) => (
                <div key={location.id} className="space-y-2">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <div className={`h-8 w-8 rounded-full ${location.color} flex items-center justify-center`}>
                        <MapPin className="h-4 w-4" />
                      </div>
                      <p className="font-medium">{location.name}</p>
                    </div>
                    <div className="text-right">
                      <p className="font-medium">{location.appointments} appointments</p>
                    </div>
                  </div>
                  <Progress value={(location.appointments / 150) * 100} className="h-2" />
                </div>
              ))}
            </div>
          </TabsContent>

          <TabsContent value="utilization">
            <div className="space-y-6">
              {locationData.map((location) => (
                <div key={location.id} className="space-y-2">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <div className={`h-8 w-8 rounded-full ${location.color} flex items-center justify-center`}>
                        <MapPin className="h-4 w-4" />
                      </div>
                      <p className="font-medium">{location.name}</p>
                    </div>
                    <div className="text-right">
                      <p className="font-medium">{location.utilization}%</p>
                    </div>
                  </div>
                  <Progress value={location.utilization} className="h-2" />
                </div>
              ))}
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  )
}

