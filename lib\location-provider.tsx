"use client"

import React, { createContext, useContext, useState, useEffect, useCallback } from "react"
import { SettingsStorage, Location } from "@/lib/settings-storage"
import { locationEventBus } from "@/lib/location-event-bus"
import { locationCache } from "@/lib/location-cache"

interface LocationContextType {
  locations: Location[]
  getLocationById: (id: string) => Location | undefined
  getLocationName: (id: string) => string
  getLocationIds: () => string[]
  getActiveLocations: () => Location[]
  refreshLocations: () => void
  isHomeServiceEnabled: boolean
  addLocation: (location: Location) => void
  updateLocation: (location: Location) => void
  deleteLocation: (locationId: string) => void
  syncLocations: () => void
}

const LocationContext = createContext<LocationContextType>({
  locations: [],
  getLocationById: () => undefined,
  getLocationName: () => "Unknown Location",
  getLocationIds: () => [],
  getActiveLocations: () => [],
  refreshLocations: () => {},
  isHomeServiceEnabled: false,
  addLocation: () => {},
  updateLocation: () => {},
  deleteLocation: () => {},
  syncLocations: () => {},
})

export function LocationProvider({ children }: { children: React.ReactNode }) {
  const [locations, setLocations] = useState<Location[]>([])
  const [isHomeServiceEnabled, setIsHomeServiceEnabled] = useState(false)

  // Load locations from storage and cache
  const loadLocations = useCallback(() => {
    // Initialize the location cache if needed
    locationCache.initialize()

    // Get locations from cache
    const cachedLocations = locationCache.getAllLocations()

    // Filter out any location with id "home" or name "Home Service" to prevent duplicates
    const filteredLocations = cachedLocations.filter(
      loc => loc.id !== "home" && loc.name !== "Home Service"
    )

    setLocations(filteredLocations)

    // Check if home service is enabled (for future use)
    setIsHomeServiceEnabled(true) // For now, always enable home service

    // We're no longer publishing the 'locations-refreshed' event here
    // to avoid infinite loops with the event subscribers
    console.log("Locations loaded:", filteredLocations.length);
  }, [])

  // Load locations on mount
  useEffect(() => {
    loadLocations()

    // Subscribe to specific location events, excluding 'locations-refreshed'
    // to avoid infinite loops
    const unsubscribeAdded = locationEventBus.subscribe('location-added', () => {
      // Only reload locations when a location is added
      const cachedLocations = locationCache.getAllLocations();
      const filteredLocations = cachedLocations.filter(
        loc => loc.id !== "home" && loc.name !== "Home Service"
      );
      setLocations(filteredLocations);
      setIsHomeServiceEnabled(true);
    });

    const unsubscribeUpdated = locationEventBus.subscribe('location-updated', () => {
      // Only reload locations when a location is updated
      const cachedLocations = locationCache.getAllLocations();
      const filteredLocations = cachedLocations.filter(
        loc => loc.id !== "home" && loc.name !== "Home Service"
      );
      setLocations(filteredLocations);
    });

    const unsubscribeRemoved = locationEventBus.subscribe('location-removed', () => {
      // Only reload locations when a location is removed
      const cachedLocations = locationCache.getAllLocations();
      const filteredLocations = cachedLocations.filter(
        loc => loc.id !== "home" && loc.name !== "Home Service"
      );
      setLocations(filteredLocations);
    });

    const unsubscribeCurrentChanged = locationEventBus.subscribe('current-location-changed', () => {
      // No need to reload locations when current location changes
      // This is handled by the component that changes the current location
    });

    return () => {
      // Unsubscribe when component unmounts
      unsubscribeAdded();
      unsubscribeUpdated();
      unsubscribeRemoved();
      unsubscribeCurrentChanged();
    }
  }, [loadLocations])

  // Get location by ID (using cache)
  const getLocationById = useCallback((id: string): Location | undefined => {
    return locationCache.getLocationById(id)
  }, [])

  // Get location name by ID (using cache)
  const getLocationName = useCallback((id: string): string => {
    return locationCache.getLocationName(id)
  }, [])

  // Get all location IDs
  const getLocationIds = useCallback((): string[] => {
    return locations.map(location => location.id)
  }, [locations])

  // Get active locations
  const getActiveLocations = useCallback((): Location[] => {
    return locationCache.getActiveLocations()
  }, [])

  // Refresh locations from storage and cache
  const refreshLocations = useCallback(() => {
    // Refresh the cache
    locationCache.refreshCache()

    // Get locations from cache directly instead of using loadLocations
    // to avoid potential event publishing loops
    const cachedLocations = locationCache.getAllLocations()

    // Filter out any location with id "home" or name "Home Service" to prevent duplicates
    const filteredLocations = cachedLocations.filter(
      loc => loc.id !== "home" && loc.name !== "Home Service"
    )

    // Update state only if the locations have changed
    if (JSON.stringify(filteredLocations) !== JSON.stringify(locations)) {
      setLocations(filteredLocations)
    }
  }, [locations])

  // Add a new location
  const addLocation = useCallback((location: Location) => {
    // Validate location name and prevent reserved names
    if (!location.name || location.name.trim() === "") {
      console.warn("Cannot add a location without a name")
      return
    }
    if (location.id === "home" || location.name === "Home Service") {
      console.warn("Cannot add a location with id 'home' or name 'Home Service' as it's reserved for the special Home Service location")
      return
    }

    console.log("Adding location:", location.name);

    // Add location to storage
    SettingsStorage.addLocation(location)

    // Refresh the cache
    locationCache.refreshCache()

    // Get updated locations from cache
    const cachedLocations = locationCache.getAllLocations()

    // Filter out any location with id "home" or name "Home Service" to prevent duplicates
    const filteredLocations = cachedLocations.filter(
      loc => loc.id !== "home" && loc.name !== "Home Service"
    )

    // Update state directly
    setLocations(filteredLocations)

    // Publish location-added event
    locationEventBus.publish({
      type: 'location-added',
      payload: location
    })

    console.log("Location added:", location.name);
  }, [])

  // Update an existing location
  const updateLocation = useCallback((location: Location) => {
    // Validate location name and prevent reserved names
    if (!location.name || location.name.trim() === "") {
      console.warn("Cannot update a location without a name")
      return
    }
    if (location.id !== "home" && location.name === "Home Service") {
      console.warn("Cannot update a location to have name 'Home Service' as it's reserved for the special Home Service location")
      return
    }

    console.log("Updating location:", location.name, location.id);

    // Special handling for Home Service location
    if (location.id === "home") {
      console.log("Updating Home Service location with phone:", location.phone);

      // Update the special location in the cache
      locationCache.updateHomeServiceLocation(location)

      // Get updated locations from cache
      const cachedLocations = locationCache.getAllLocations()

      // Filter out any location with id "home" or name "Home Service" to prevent duplicates
      const filteredLocations = cachedLocations.filter(
        loc => loc.id !== "home" && loc.name !== "Home Service"
      )

      // Update state directly
      setLocations(filteredLocations)

      // Publish location-updated event
      locationEventBus.publish({
        type: 'location-updated',
        payload: location
      })

      console.log("Home Service location updated with phone:", location.phone);
      return
    }

    // Update regular location in storage
    SettingsStorage.updateLocation(location)

    // Refresh the cache
    locationCache.refreshCache()

    // Get updated locations from cache
    const cachedLocations = locationCache.getAllLocations()

    // Filter out any location with id "home" or name "Home Service" to prevent duplicates
    const filteredLocations = cachedLocations.filter(
      loc => loc.id !== "home" && loc.name !== "Home Service"
    )

    // Update state directly
    setLocations(filteredLocations)

    // Publish location-updated event
    locationEventBus.publish({
      type: 'location-updated',
      payload: location
    })

    console.log("Location updated:", location.name);
  }, [])

  // Delete a location
  const deleteLocation = useCallback((locationId: string) => {
    console.log("Deleting location:", locationId);

    // Special handling for home service location
    if (locationId === "home") {
      // For home service, we don't actually delete it, but we can reset it to default
      // This is handled in the component that calls this function
      console.log("Resetting Home Service location to default settings")

      // Clear any stored home service location customizations
      if (typeof window !== 'undefined') {
        localStorage.removeItem("vanity_home_service_location")
      }

      // Refresh the cache to restore default home service
      locationCache.refreshCache()

      // Get updated locations from cache
      const cachedLocations = locationCache.getAllLocations()

      // Filter out any location with id "home" or name "Home Service" to prevent duplicates
      const filteredLocations = cachedLocations.filter(
        loc => loc.id !== "home" && loc.name !== "Home Service"
      )

      // Update state directly
      setLocations(filteredLocations)

      console.log("Home Service location reset to default");
      return
    }

    // Delete regular location from storage
    SettingsStorage.deleteLocation(locationId)

    // Refresh the cache
    locationCache.refreshCache()

    // Get updated locations from cache
    const cachedLocations = locationCache.getAllLocations()

    // Filter out any location with id "home" or name "Home Service" to prevent duplicates
    const filteredLocations = cachedLocations.filter(
      loc => loc.id !== "home" && loc.name !== "Home Service"
    )

    // Update state directly
    setLocations(filteredLocations)

    // Publish location-removed event
    locationEventBus.publish({
      type: 'location-removed',
      payload: locationId
    })

    console.log("Location deleted:", locationId);
  }, [])

  // Synchronize locations across the application
  const syncLocations = useCallback(() => {
    // Refresh the cache
    locationCache.refreshCache()

    // Get locations from cache directly instead of using loadLocations
    // to avoid potential event publishing loops
    const cachedLocations = locationCache.getAllLocations()

    // Filter out any location with id "home" or name "Home Service" to prevent duplicates
    const filteredLocations = cachedLocations.filter(
      loc => loc.id !== "home" && loc.name !== "Home Service"
    )

    // Update state only if the locations have changed
    if (JSON.stringify(filteredLocations) !== JSON.stringify(locations)) {
      setLocations(filteredLocations)
    }

    // Only update if needed
    if (!isHomeServiceEnabled) {
      setIsHomeServiceEnabled(true)
    }
  }, [locations, isHomeServiceEnabled])

  // Helper function to check if a location is a Home Service location
  const isHomeServiceLocation = useCallback((location: Location): boolean => {
    return location.id === "home" || location.name === "Home Service"
  }, [])

  return (
    <LocationContext.Provider
      value={{
        locations,
        getLocationById,
        getLocationName,
        getLocationIds,
        getActiveLocations,
        refreshLocations,
        isHomeServiceEnabled,
        addLocation,
        updateLocation,
        deleteLocation,
        syncLocations,
      }}
    >
      {children}
    </LocationContext.Provider>
  )
}

export const useLocations = () => useContext(LocationContext)
