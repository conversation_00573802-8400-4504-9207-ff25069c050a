"use client"

import * as React from "react"
import { useAuth } from "@/lib/auth-provider"
import { useLocations } from "@/lib/location-provider"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"

export function LocationSelector() {
  const { currentLocation, setCurrentLocation } = useAuth()
  const { locations, getLocationName, isHomeServiceEnabled } = useLocations()

  // Memoize the filtered locations to prevent unnecessary re-renders
  const filteredLocations = React.useMemo(() => {
    return locations
      .filter(location => location.status === "Active" && location.name && location.name.trim() !== "")
      .filter(location => !!location.id); // Filter out locations without a valid ID
  }, [locations]);

  const handleLocationChange = React.useCallback((newLocation: string) => {
    setCurrentLocation(newLocation)
  }, [setCurrentLocation]);

  // Memoize the entire select component to prevent unnecessary re-renders
  const selectComponent = React.useMemo(() => {
    return (
      <Select
        value={currentLocation || "all"}
        onValueChange={handleLocationChange}
      >
        <SelectTrigger className="w-[180px] bg-muted/50 border-0">
          <SelectValue placeholder="Select location" />
        </SelectTrigger>
        <SelectContent>
          <SelectItem value="all">All Locations</SelectItem>

          {/* Map through active locations from settings */}
          {filteredLocations.map(location => (
            <SelectItem key={location.id} value={location.id}>
              {location.name}
            </SelectItem>
          ))}

          {/* Add Home Service option if enabled */}
          {isHomeServiceEnabled && (
            <SelectItem value="home" key="home">Home Service</SelectItem>
          )}
        </SelectContent>
      </Select>
    );
  }, [currentLocation, handleLocationChange, filteredLocations, isHomeServiceEnabled]);

  return selectComponent;
}

