"use client"

import { useState, useEffect } from "react"
import { Label } from "@/components/ui/label"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Checkbox } from "@/components/ui/checkbox"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { X } from "lucide-react"
import { mockServices, mockStaff } from "@/lib/mock-data"
import { ClientPreferences } from "@/lib/client-provider"

interface ClientPreferencesFormProps {
  initialPreferences?: ClientPreferences
  onSave: (preferences: ClientPreferences) => void
}

export function ClientPreferencesForm({ initialPreferences, onSave }: ClientPreferencesFormProps) {
  // Initialize with empty arrays or initial values if provided
  const [preferredStylists, setPreferredStylists] = useState<string[]>([])
  const [preferredServices, setPreferredServices] = useState<string[]>([])
  const [preferredProducts, setPreferredProducts] = useState<string[]>([])
  const [allergies, setAllergies] = useState<string[]>(initialPreferences?.allergies || [])
  const [newAllergy, setNewAllergy] = useState("")
  const [notes, setNotes] = useState(initialPreferences?.notes || "")

  // Load initial preferences when component mounts or initialPreferences changes
  useEffect(() => {
    if (initialPreferences) {
      // For stylists and services, we need to find the IDs from the names
      const stylistIds = initialPreferences.preferredStylists.map(name => {
        const staff = mockStaff.find(s => s.name === name)
        return staff ? staff.id : ""
      }).filter(Boolean)

      const serviceIds = initialPreferences.preferredServices.map(name => {
        const service = mockServices.find(s => s.name === name)
        return service ? service.id : ""
      }).filter(Boolean)

      setPreferredStylists(stylistIds)
      setPreferredServices(serviceIds)
      setAllergies(initialPreferences.allergies)
      setNotes(initialPreferences.notes)
    }
  }, [initialPreferences])

  // Filter staff to only include stylists and related roles
  const stylists = mockStaff.filter(staff =>
    ["stylist", "colorist", "barber"].includes(staff.role)
  )

  // Filter services to common ones
  const services = mockServices.slice(0, 8)

  const handleStylistToggle = (staffId: string) => {
    setPreferredStylists(prev =>
      prev.includes(staffId)
        ? prev.filter(id => id !== staffId)
        : [...prev, staffId]
    )
  }

  const handleServiceToggle = (serviceId: string) => {
    setPreferredServices(prev =>
      prev.includes(serviceId)
        ? prev.filter(id => id !== serviceId)
        : [...prev, serviceId]
    )
  }

  const addAllergy = () => {
    if (newAllergy.trim() && !allergies.includes(newAllergy.trim())) {
      setAllergies(prev => [...prev, newAllergy.trim()])
      setNewAllergy("")
    }
  }

  const removeAllergy = (allergy: string) => {
    setAllergies(prev => prev.filter(a => a !== allergy))
  }

  const handleProductToggle = (productId: string) => {
    setPreferredProducts(prev =>
      prev.includes(productId)
        ? prev.filter(id => id !== productId)
        : [...prev, productId]
    )
  }

  const handleSubmit = () => {
    const stylistNames = preferredStylists.map(id => {
      const staff = mockStaff.find(s => s.id === id)
      return staff ? staff.name : ""
    }).filter(Boolean)

    const serviceNames = preferredServices.map(id => {
      const service = mockServices.find(s => s.id === id)
      return service ? service.name : ""
    }).filter(Boolean)

    const productNames = preferredProducts.map(id => {
      // In a real app, you would fetch this from your product data
      // For now, we'll use mock data or placeholder
      return `Product ${id}`
    })

    onSave({
      preferredStylists: stylistNames,
      preferredServices: serviceNames,
      preferredProducts: productNames,
      allergies,
      notes
    })
  }

  return (
    <div className="space-y-6">
      <div className="space-y-4">
        <div>
          <h3 className="text-sm font-medium mb-2">Preferred Stylists</h3>
          <div className="grid grid-cols-2 gap-2">
            {stylists.map((staff) => (
              <div key={staff.id} className="flex items-center space-x-2">
                <Checkbox
                  id={`stylist-${staff.id}`}
                  checked={preferredStylists.includes(staff.id)}
                  onCheckedChange={() => handleStylistToggle(staff.id)}
                />
                <Label htmlFor={`stylist-${staff.id}`} className="text-sm">
                  {staff.name}
                </Label>
              </div>
            ))}
          </div>
        </div>

        <div>
          <h3 className="text-sm font-medium mb-2">Preferred Services</h3>
          <div className="grid grid-cols-2 gap-2">
            {services.map((service) => (
              <div key={service.id} className="flex items-center space-x-2">
                <Checkbox
                  id={`service-${service.id}`}
                  checked={preferredServices.includes(service.id)}
                  onCheckedChange={() => handleServiceToggle(service.id)}
                />
                <Label htmlFor={`service-${service.id}`} className="text-sm">
                  {service.name}
                </Label>
              </div>
            ))}
          </div>
        </div>

        <div>
          <h3 className="text-sm font-medium mb-2">Allergies & Sensitivities</h3>
          <div className="flex items-center space-x-2 mb-2">
            <Input
              placeholder="Add allergy or sensitivity"
              value={newAllergy}
              onChange={(e) => setNewAllergy(e.target.value)}
              className="flex-1"
              onKeyDown={(e) => {
                if (e.key === 'Enter') {
                  e.preventDefault()
                  addAllergy()
                }
              }}
            />
            <Button type="button" onClick={addAllergy} size="sm">
              Add
            </Button>
          </div>
          {allergies.length > 0 && (
            <div className="flex flex-wrap gap-2 mt-2">
              {allergies.map((allergy) => (
                <Badge key={allergy} variant="outline" className="flex items-center gap-1">
                  {allergy}
                  <Button
                    type="button"
                    variant="ghost"
                    size="icon"
                    className="h-4 w-4 p-0 hover:bg-transparent"
                    onClick={() => removeAllergy(allergy)}
                  >
                    <X className="h-3 w-3" />
                    <span className="sr-only">Remove</span>
                  </Button>
                </Badge>
              ))}
            </div>
          )}
        </div>

        <div>
          <h3 className="text-sm font-medium mb-2">Notes & Special Instructions</h3>
          <Textarea
            placeholder="Add any special notes or instructions for this client"
            value={notes}
            onChange={(e) => setNotes(e.target.value)}
            rows={3}
          />
        </div>
      </div>

      <Button type="button" onClick={handleSubmit} className="w-full">
        Save Preferences
      </Button>
    </div>
  )
}
