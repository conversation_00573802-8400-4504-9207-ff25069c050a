"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { mockStaff } from "@/lib/mock-data"
import { Edit, Loader2, MoreHorizontal, Plus, Shield, Trash, UserPlus } from "lucide-react"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Avatar, AvatarFallback } from "@/components/ui/avatar"
import { useToast } from "@/components/ui/use-toast"
import { useUnifiedStaff } from "@/lib/unified-staff-provider"
import { PERMISSIONS, PERMISSION_CATEGORIES } from "@/lib/permissions"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Checkbox } from "@/components/ui/checkbox"
import { SettingsStorage, User, Role, Location } from "@/lib/settings-storage"

export function UserSettings() {
  const { toast } = useToast()
  const { users, updateUser, deleteUser, refreshData } = useUnifiedStaff()
  const [searchTerm, setSearchTerm] = useState("")
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [localUsers, setLocalUsers] = useState<User[]>([])
  const [roles, setRoles] = useState<Role[]>([])
  const [locations, setLocations] = useState<Location[]>([])
  const [isAddUserDialogOpen, setIsAddUserDialogOpen] = useState(false)
  const [isAddRoleDialogOpen, setIsAddRoleDialogOpen] = useState(false)
  const [isEditUserDialogOpen, setIsEditUserDialogOpen] = useState(false)
  const [isEditRoleDialogOpen, setIsEditRoleDialogOpen] = useState(false)
  const [isDeleteUserDialogOpen, setIsDeleteUserDialogOpen] = useState(false)
  const [isDeleteRoleDialogOpen, setIsDeleteRoleDialogOpen] = useState(false)
  const [userToEdit, setUserToEdit] = useState<User | null>(null)
  const [roleToEdit, setRoleToEdit] = useState<Role | null>(null)
  const [userToDelete, setUserToDelete] = useState<string | null>(null)
  const [roleToDelete, setRoleToDelete] = useState<string | null>(null)

  // Helper function to get location name by ID
  const getLocationName = (locationId: string): string => {
    if (locationId === "all") return "All Locations"
    const location = locations.find(loc => loc.id === locationId)
    return location ? location.name : "Unknown Location"
  }

  // New user form state
  const [newUser, setNewUser] = useState<Partial<User>>({
    name: "",
    email: "",
    role: "staff",
    locations: [],
    status: "Active",
    avatar: "",
    color: "bg-blue-500",
  })

  // New role form state
  const [newRole, setNewRole] = useState<Partial<Role>>({
    name: "",
    description: "",
    permissions: [],
  })

  // Load data from storage
  useEffect(() => {
    // Use users from the unified provider
    setLocalUsers(users)

    // Refresh data to ensure it's up to date
    refreshData()

    // Load roles
    const storedRoles = SettingsStorage.getRoles()
    if (storedRoles.length === 0) {
      // Initialize with default roles if empty
      const initialRoles = [
        {
          id: "super_admin",
          name: "Super Admin",
          description: "Full access to all settings and features across all locations",
          userCount: 1,
          permissions: ["all"],
        },
        {
          id: "org_admin",
          name: "Organization Admin",
          description: "Access to organization-wide settings and features",
          userCount: 2,
          permissions: [
            "manage_locations",
            "manage_staff",
            "manage_services",
            "manage_clients",
            "manage_inventory",
            "manage_reports",
          ],
        },
        {
          id: "location_manager",
          name: "Location Manager",
          description: "Access to settings and features for assigned locations",
          userCount: 3,
          permissions: ["manage_staff", "manage_services", "manage_clients", "manage_inventory", "view_reports"],
        },
        {
          id: "staff",
          name: "Staff",
          description: "Access to appointments, clients, and services",
          userCount: 15,
          permissions: ["view_appointments", "manage_own_appointments", "view_clients", "view_services"],
        },
        {
          id: "receptionist",
          name: "Receptionist",
          description: "Access to appointments, clients, and point of sale",
          userCount: 5,
          permissions: ["view_appointments", "manage_appointments", "view_clients", "manage_clients", "use_pos"],
        },
      ]
      setRoles(initialRoles)
      SettingsStorage.saveRoles(initialRoles)
    } else {
      // Update user counts in roles
      const rolesWithCounts = storedRoles.map(role => {
        const count = users.filter(user => user.role === role.id).length;
        return { ...role, userCount: count };
      });
      setRoles(rolesWithCounts);
      SettingsStorage.saveRoles(rolesWithCounts);
    }

    // Load locations
    setLocations(SettingsStorage.getLocations())
  }, [])

  // Filter users based on search term
  const filteredUsers = users.filter(
    (user) =>
      user.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      user.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
      user.role.toLowerCase().includes(searchTerm.toLowerCase()),
  )

  // Update local users when unified users change
  useEffect(() => {
    setLocalUsers(users)
  }, [users])

  return (
    <div className="space-y-6">
      <Tabs defaultValue="users">
        <TabsList className="mb-4">
          <TabsTrigger value="users">Users</TabsTrigger>
          <TabsTrigger value="roles">Roles & Permissions</TabsTrigger>
        </TabsList>

        <TabsContent value="users">
          <div className="flex justify-between items-center mb-4">
            <div className="relative w-full max-w-sm">
              <Input
                placeholder="Search users..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-8"
              />
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                />
              </svg>
            </div>
            <Dialog open={isAddUserDialogOpen} onOpenChange={setIsAddUserDialogOpen}>
              <DialogTrigger asChild>
                <Button>
                  <UserPlus className="mr-2 h-4 w-4" />
                  Add User
                </Button>
              </DialogTrigger>
              <DialogContent className="sm:max-w-[600px]">
                <DialogHeader>
                  <DialogTitle>Add New User</DialogTitle>
                  <DialogDescription>
                    Add a new user to your organization.
                  </DialogDescription>
                </DialogHeader>
                <div className="grid gap-4 py-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="new-name">Full Name</Label>
                      <Input
                        id="new-name"
                        value={newUser.name as string}
                        onChange={(e) => setNewUser({...newUser, name: e.target.value})}
                        required
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="new-email">Email Address</Label>
                      <Input
                        id="new-email"
                        type="email"
                        value={newUser.email as string}
                        onChange={(e) => setNewUser({...newUser, email: e.target.value})}
                        required
                      />
                    </div>
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="new-role">Role</Label>
                      <Select
                        value={newUser.role as string}
                        onValueChange={(value) => setNewUser({...newUser, role: value})}
                      >
                        <SelectTrigger id="new-role">
                          <SelectValue placeholder="Select role" />
                        </SelectTrigger>
                        <SelectContent>
                          {roles.map(role => (
                            <SelectItem key={role.id} value={role.id}>{role.name}</SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="new-status">Status</Label>
                      <Select
                        value={newUser.status as string}
                        onValueChange={(value) => setNewUser({...newUser, status: value})}
                      >
                        <SelectTrigger id="new-status">
                          <SelectValue placeholder="Select status" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="Active">Active</SelectItem>
                          <SelectItem value="Inactive">Inactive</SelectItem>
                          <SelectItem value="Pending">Pending</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label>Assigned Locations</Label>
                    <div className="grid grid-cols-2 gap-2 mt-2">
                      {locations.map(location => (
                        <div key={location.id} className="flex items-center space-x-2">
                          <Checkbox
                            id={`location-${location.id}`}
                            checked={newUser.locations?.includes(location.id)}
                            onCheckedChange={(checked) => {
                              if (checked) {
                                setNewUser({
                                  ...newUser,
                                  locations: [...(newUser.locations || []), location.id]
                                })
                              } else {
                                setNewUser({
                                  ...newUser,
                                  locations: newUser.locations?.filter(id => id !== location.id) || []
                                })
                              }
                            }}
                          />
                          <Label htmlFor={`location-${location.id}`} className="text-sm font-normal">
                            {location.name}
                          </Label>
                        </div>
                      ))}
                      <div className="flex items-center space-x-2">
                        <Checkbox
                          id="location-all"
                          checked={newUser.locations?.includes("all")}
                          onCheckedChange={(checked) => {
                            if (checked) {
                              setNewUser({...newUser, locations: ["all"]})
                            } else {
                              setNewUser({...newUser, locations: []})
                            }
                          }}
                        />
                        <Label htmlFor="location-all" className="text-sm font-normal">
                          All Locations
                        </Label>
                      </div>
                    </div>
                  </div>
                </div>
                <DialogFooter>
                  <Button variant="outline" onClick={() => setIsAddUserDialogOpen(false)}>Cancel</Button>
                  <Button
                    onClick={() => {
                      setIsSubmitting(true)

                      try {
                        // Generate a unique ID
                        const newId = `user${Date.now()}`

                        // Create avatar from initials
                        const nameParts = newUser.name?.split(" ") || []
                        const avatar = nameParts.length > 1
                          ? `${nameParts[0][0]}${nameParts[nameParts.length - 1][0]}`
                          : newUser.name?.substring(0, 2) || "U"

                        // Create the new user object
                        const userToAdd: User = {
                          id: newId,
                          name: newUser.name as string,
                          email: newUser.email as string,
                          role: newUser.role as string,
                          locations: newUser.locations as string[],
                          status: newUser.status as string,
                          avatar: avatar.toUpperCase(),
                          color: newUser.color as string,
                          lastLogin: "Never",
                        }

                        // Add user through the unified service
                        const result = updateUser(userToAdd)

                        // Refresh data
                        refreshData()

                        // Reset form and close dialog
                        setNewUser({
                          name: "",
                          email: "",
                          role: "staff",
                          locations: [],
                          status: "Active",
                          avatar: "",
                          color: "bg-blue-500",
                        })
                        setIsAddUserDialogOpen(false)

                        toast({
                          title: "User added",
                          description: "New user has been added successfully.",
                        })
                      } catch (error) {
                        console.error("Failed to add user:", error)
                        toast({
                          variant: "destructive",
                          title: "Error",
                          description: "Failed to add user. Please try again.",
                        })
                      } finally {
                        setIsSubmitting(false)
                      }
                    }}
                    disabled={isSubmitting || !newUser.name || !newUser.email || !newUser.role || newUser.locations?.length === 0}
                  >
                    {isSubmitting ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Adding...
                      </>
                    ) : (
                      "Add User"
                    )}
                  </Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>

            {/* Edit User Dialog */}
            <Dialog open={isEditUserDialogOpen} onOpenChange={setIsEditUserDialogOpen}>
              <DialogContent className="sm:max-w-[600px]">
                <DialogHeader>
                  <DialogTitle>Edit User</DialogTitle>
                  <DialogDescription>
                    Update user information and permissions.
                  </DialogDescription>
                </DialogHeader>
                {userToEdit && (
                  <div className="grid gap-4 py-4">
                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="edit-name">Full Name</Label>
                        <Input
                          id="edit-name"
                          value={newUser.name || userToEdit.name}
                          onChange={(e) => setNewUser({...newUser, name: e.target.value})}
                          required
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="edit-email">Email Address</Label>
                        <Input
                          id="edit-email"
                          type="email"
                          value={newUser.email || userToEdit.email}
                          onChange={(e) => setNewUser({...newUser, email: e.target.value})}
                          required
                        />
                      </div>
                    </div>

                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="edit-role">Role</Label>
                        <Select
                          value={newUser.role || userToEdit.role}
                          onValueChange={(value) => setNewUser({...newUser, role: value})}
                        >
                          <SelectTrigger id="edit-role">
                            <SelectValue placeholder="Select role" />
                          </SelectTrigger>
                          <SelectContent>
                            {roles.map(role => (
                              <SelectItem key={role.id} value={role.id}>{role.name}</SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="edit-status">Status</Label>
                        <Select
                          value={newUser.status || userToEdit.status}
                          onValueChange={(value) => setNewUser({...newUser, status: value})}
                        >
                          <SelectTrigger id="edit-status">
                            <SelectValue placeholder="Select status" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="Active">Active</SelectItem>
                            <SelectItem value="Inactive">Inactive</SelectItem>
                            <SelectItem value="Pending">Pending</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>

                    <div className="space-y-2">
                      <Label>Assigned Locations</Label>
                      <div className="grid grid-cols-2 gap-2 mt-2">
                        {locations.map(location => (
                          <div key={location.id} className="flex items-center space-x-2">
                            <Checkbox
                              id={`edit-location-${location.id}`}
                              checked={(newUser.locations || userToEdit.locations)?.includes(location.id)}
                              onCheckedChange={(checked) => {
                                const currentLocations = newUser.locations || userToEdit.locations;
                                if (checked) {
                                  setNewUser({
                                    ...newUser,
                                    locations: [...currentLocations.filter(id => id !== "all"), location.id]
                                  })
                                } else {
                                  setNewUser({
                                    ...newUser,
                                    locations: currentLocations.filter(id => id !== location.id)
                                  })
                                }
                              }}
                            />
                            <Label htmlFor={`edit-location-${location.id}`} className="text-sm font-normal">
                              {location.name}
                            </Label>
                          </div>
                        ))}
                        <div className="flex items-center space-x-2">
                          <Checkbox
                            id="edit-location-all"
                            checked={(newUser.locations || userToEdit.locations)?.includes("all")}
                            onCheckedChange={(checked) => {
                              if (checked) {
                                setNewUser({...newUser, locations: ["all"]})
                              } else {
                                setNewUser({...newUser, locations: []})
                              }
                            }}
                          />
                          <Label htmlFor="edit-location-all" className="text-sm font-normal">
                            All Locations
                          </Label>
                        </div>
                      </div>
                    </div>
                  </div>
                )}
                <DialogFooter>
                  <Button variant="outline" onClick={() => {
                    setIsEditUserDialogOpen(false)
                    setUserToEdit(null)
                    setNewUser({
                      name: "",
                      email: "",
                      role: "staff",
                      locations: [],
                      status: "Active",
                      avatar: "",
                      color: "bg-blue-500",
                    })
                  }}>Cancel</Button>
                  <Button
                    onClick={() => {
                      setIsSubmitting(true)

                      try {
                        if (!userToEdit) return;

                        // Create the updated user object
                        const updatedUser: User = {
                          ...userToEdit,
                          name: newUser.name || userToEdit.name,
                          email: newUser.email || userToEdit.email,
                          role: newUser.role || userToEdit.role,
                          locations: newUser.locations || userToEdit.locations,
                          status: newUser.status || userToEdit.status,
                        }

                        // Update user through the unified service
                        const result = updateUser(updatedUser)

                        // Refresh data
                        refreshData()

                        // Reset form and close dialog
                        setNewUser({
                          name: "",
                          email: "",
                          role: "staff",
                          locations: [],
                          status: "Active",
                          avatar: "",
                          color: "bg-blue-500",
                        })
                        setUserToEdit(null)
                        setIsEditUserDialogOpen(false)

                        toast({
                          title: "User updated",
                          description: "User has been updated successfully.",
                        })
                      } catch (error) {
                        console.error("Failed to update user:", error)
                        toast({
                          variant: "destructive",
                          title: "Error",
                          description: "Failed to update user. Please try again.",
                        })
                      } finally {
                        setIsSubmitting(false)
                      }
                    }}
                    disabled={isSubmitting || !userToEdit}
                  >
                    {isSubmitting ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Updating...
                      </>
                    ) : (
                      "Update User"
                    )}
                  </Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>

            {/* Delete User Confirmation Dialog */}
            <AlertDialog open={isDeleteUserDialogOpen} onOpenChange={setIsDeleteUserDialogOpen}>
              <AlertDialogContent>
                <AlertDialogHeader>
                  <AlertDialogTitle>Are you sure?</AlertDialogTitle>
                  <AlertDialogDescription>
                    This action cannot be undone. This will permanently delete the user and remove their data from the system.
                  </AlertDialogDescription>
                </AlertDialogHeader>
                <AlertDialogFooter>
                  <AlertDialogCancel onClick={() => setUserToDelete(null)}>Cancel</AlertDialogCancel>
                  <AlertDialogAction
                    onClick={() => {
                      if (!userToDelete) return;

                      try {
                        // Delete user through the unified service
                        const result = deleteUser(userToDelete)

                        // Refresh data
                        refreshData()

                        // Reset and close dialog
                        setUserToDelete(null)
                        setIsDeleteUserDialogOpen(false)

                        toast({
                          title: "User deleted",
                          description: "User has been deleted successfully.",
                        })
                      } catch (error) {
                        console.error("Failed to delete user:", error)
                        toast({
                          variant: "destructive",
                          title: "Error",
                          description: "Failed to delete user. Please try again.",
                        })
                      }
                    }}
                    className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                  >
                    Delete
                  </AlertDialogAction>
                </AlertDialogFooter>
              </AlertDialogContent>
            </AlertDialog>
          </div>

          <Card>
            <CardContent className="p-0">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Name</TableHead>
                    <TableHead>Email</TableHead>
                    <TableHead>Role</TableHead>
                    <TableHead>Locations</TableHead>
                    <TableHead>Last Login</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredUsers.map((user) => (
                    <TableRow key={user.id}>
                      <TableCell>
                        <div className="flex items-center gap-3">
                          <Avatar className={`h-8 w-8 ${user.color}`}>
                            <AvatarFallback>{user.avatar}</AvatarFallback>
                          </Avatar>
                          <span className="font-medium">{user.name}</span>
                        </div>
                      </TableCell>
                      <TableCell>{user.email}</TableCell>
                      <TableCell>
                        <Badge variant="outline">
                          {user.role === "super_admin"
                            ? "Super Admin"
                            : user.role === "org_admin"
                              ? "Organization Admin"
                              : user.role === "location_manager"
                                ? "Location Manager"
                                : user.role === "receptionist"
                                  ? "Receptionist"
                                  : "Staff"}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <div className="flex flex-wrap gap-1">
                          {user.locations.map((loc) => (
                            <Badge key={loc} variant="secondary" className="text-xs">
                              {getLocationName(loc)}
                            </Badge>
                          ))}
                        </div>
                      </TableCell>
                      <TableCell>{user.lastLogin}</TableCell>
                      <TableCell>
                        <Badge variant={user.status === "Active" ? "success" : "secondary"}>{user.status}</Badge>
                      </TableCell>
                      <TableCell className="text-right">
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" size="icon">
                              <MoreHorizontal className="h-4 w-4" />
                              <span className="sr-only">Open menu</span>
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuItem onClick={() => {
                              setUserToEdit(user)
                              setIsEditUserDialogOpen(true)
                            }}>
                              <Edit className="mr-2 h-4 w-4" />
                              Edit user
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={() => {
                              setUserToEdit(user)
                              // Pre-populate the edit form with the current user data
                              setNewUser({
                                ...user,
                              })
                              setIsEditUserDialogOpen(true)
                            }}>
                              <Shield className="mr-2 h-4 w-4" />
                              Change role
                            </DropdownMenuItem>
                            <DropdownMenuItem>Reset password</DropdownMenuItem>
                            <DropdownMenuItem
                              className="text-destructive focus:text-destructive"
                              onClick={() => {
                                setUserToDelete(user.id)
                                setIsDeleteUserDialogOpen(true)
                              }}
                            >
                              <Trash className="mr-2 h-4 w-4" />
                              Delete user
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>
                  ))}
                  {filteredUsers.length === 0 && (
                    <TableRow>
                      <TableCell colSpan={7} className="h-24 text-center">
                        No users found.
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="roles">
          <div className="flex justify-end mb-4">
            <Dialog open={isAddRoleDialogOpen} onOpenChange={setIsAddRoleDialogOpen}>
              <DialogTrigger asChild>
                <Button>
                  <Plus className="mr-2 h-4 w-4" />
                  Create Role
                </Button>
              </DialogTrigger>
              <DialogContent className="sm:max-w-[600px]">
                <DialogHeader>
                  <DialogTitle>Create New Role</DialogTitle>
                  <DialogDescription>
                    Add a new role with specific permissions.
                  </DialogDescription>
                </DialogHeader>
                <div className="grid gap-4 py-4">
                  <div className="grid grid-cols-1 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="new-role-name">Role Name</Label>
                      <Input
                        id="new-role-name"
                        value={newRole.name as string}
                        onChange={(e) => setNewRole({...newRole, name: e.target.value})}
                        required
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="new-role-description">Description</Label>
                      <Input
                        id="new-role-description"
                        value={newRole.description as string}
                        onChange={(e) => setNewRole({...newRole, description: e.target.value})}
                        required
                      />
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label>Permissions</Label>
                    <div className="border rounded-md p-4 space-y-4">
                      <div className="space-y-2">
                        <div className="flex items-center space-x-2">
                          <Checkbox
                            id="permission-all"
                            checked={newRole.permissions?.includes(PERMISSIONS.ALL)}
                            onCheckedChange={(checked) => {
                              if (checked) {
                                setNewRole({...newRole, permissions: [PERMISSIONS.ALL]})
                              } else {
                                setNewRole({...newRole, permissions: []})
                              }
                            }}
                          />
                          <Label htmlFor="permission-all" className="font-medium">
                            All Permissions (Super Admin)
                          </Label>
                        </div>
                      </div>

                      {!newRole.permissions?.includes(PERMISSIONS.ALL) && (
                        <>
                          {/* Dashboard Permissions */}
                          <div className="space-y-2">
                            <h4 className="text-sm font-medium mb-2">Dashboard</h4>
                            <div className="grid grid-cols-2 gap-2">
                              <div className="flex items-center space-x-2">
                                <Checkbox
                                  id={`permission-${PERMISSIONS.VIEW_DASHBOARD}`}
                                  checked={newRole.permissions?.includes(PERMISSIONS.VIEW_DASHBOARD)}
                                  onCheckedChange={(checked) => {
                                    if (checked) {
                                      setNewRole({
                                        ...newRole,
                                        permissions: [...(newRole.permissions || []), PERMISSIONS.VIEW_DASHBOARD]
                                      })
                                    } else {
                                      setNewRole({
                                        ...newRole,
                                        permissions: newRole.permissions?.filter(p => p !== PERMISSIONS.VIEW_DASHBOARD) || []
                                      })
                                    }
                                  }}
                                />
                                <Label htmlFor={`permission-${PERMISSIONS.VIEW_DASHBOARD}`} className="text-sm font-normal">
                                  View Dashboard
                                </Label>
                              </div>
                            </div>
                          </div>

                          {/* Staff Permissions */}
                          <div className="space-y-2">
                            <h4 className="text-sm font-medium mb-2">Staff Management</h4>
                            <div className="grid grid-cols-2 gap-2">
                              {[
                                PERMISSIONS.VIEW_STAFF,
                                PERMISSIONS.CREATE_STAFF,
                                PERMISSIONS.EDIT_STAFF,
                                PERMISSIONS.DELETE_STAFF,
                                PERMISSIONS.VIEW_STAFF_SCHEDULE,
                                PERMISSIONS.EDIT_STAFF_SCHEDULE,
                                PERMISSIONS.VIEW_OWN_SCHEDULE,
                                PERMISSIONS.EDIT_OWN_SCHEDULE
                              ].map(permission => (
                                <div key={permission} className="flex items-center space-x-2">
                                  <Checkbox
                                    id={`permission-${permission}`}
                                    checked={newRole.permissions?.includes(permission)}
                                    onCheckedChange={(checked) => {
                                      if (checked) {
                                        setNewRole({
                                          ...newRole,
                                          permissions: [...(newRole.permissions || []), permission]
                                        })
                                      } else {
                                        setNewRole({
                                          ...newRole,
                                          permissions: newRole.permissions?.filter(p => p !== permission) || []
                                        })
                                      }
                                    }}
                                  />
                                  <Label htmlFor={`permission-${permission}`} className="text-sm font-normal">
                                    {permission.split('_').map(word => word.charAt(0).toUpperCase() + word.slice(1)).join(' ')}
                                  </Label>
                                </div>
                              ))}
                            </div>
                          </div>

                          {/* Client Permissions */}
                          <div className="space-y-2">
                            <h4 className="text-sm font-medium mb-2">Client Management</h4>
                            <div className="grid grid-cols-2 gap-2">
                              {[
                                PERMISSIONS.VIEW_CLIENTS,
                                PERMISSIONS.CREATE_CLIENT,
                                PERMISSIONS.EDIT_CLIENT,
                                PERMISSIONS.DELETE_CLIENT,
                                PERMISSIONS.VIEW_OWN_CLIENTS
                              ].map(permission => (
                                <div key={permission} className="flex items-center space-x-2">
                                  <Checkbox
                                    id={`permission-${permission}`}
                                    checked={newRole.permissions?.includes(permission)}
                                    onCheckedChange={(checked) => {
                                      if (checked) {
                                        setNewRole({
                                          ...newRole,
                                          permissions: [...(newRole.permissions || []), permission]
                                        })
                                      } else {
                                        setNewRole({
                                          ...newRole,
                                          permissions: newRole.permissions?.filter(p => p !== permission) || []
                                        })
                                      }
                                    }}
                                  />
                                  <Label htmlFor={`permission-${permission}`} className="text-sm font-normal">
                                    {permission.split('_').map(word => word.charAt(0).toUpperCase() + word.slice(1)).join(' ')}
                                  </Label>
                                </div>
                              ))}
                            </div>
                          </div>

                          {/* Appointment Permissions */}
                          <div className="space-y-2">
                            <h4 className="text-sm font-medium mb-2">Appointment Management</h4>
                            <div className="grid grid-cols-2 gap-2">
                              {[
                                PERMISSIONS.VIEW_APPOINTMENTS,
                                PERMISSIONS.CREATE_APPOINTMENT,
                                PERMISSIONS.EDIT_APPOINTMENT,
                                PERMISSIONS.DELETE_APPOINTMENT,
                                PERMISSIONS.VIEW_OWN_APPOINTMENTS,
                                PERMISSIONS.EDIT_OWN_APPOINTMENTS
                              ].map(permission => (
                                <div key={permission} className="flex items-center space-x-2">
                                  <Checkbox
                                    id={`permission-${permission}`}
                                    checked={newRole.permissions?.includes(permission)}
                                    onCheckedChange={(checked) => {
                                      if (checked) {
                                        setNewRole({
                                          ...newRole,
                                          permissions: [...(newRole.permissions || []), permission]
                                        })
                                      } else {
                                        setNewRole({
                                          ...newRole,
                                          permissions: newRole.permissions?.filter(p => p !== permission) || []
                                        })
                                      }
                                    }}
                                  />
                                  <Label htmlFor={`permission-${permission}`} className="text-sm font-normal">
                                    {permission.split('_').map(word => word.charAt(0).toUpperCase() + word.slice(1)).join(' ')}
                                  </Label>
                                </div>
                              ))}
                            </div>
                          </div>

                          {/* Service Permissions */}
                          <div className="space-y-2">
                            <h4 className="text-sm font-medium mb-2">Service Management</h4>
                            <div className="grid grid-cols-2 gap-2">
                              {[
                                PERMISSIONS.VIEW_SERVICES,
                                PERMISSIONS.CREATE_SERVICE,
                                PERMISSIONS.EDIT_SERVICE,
                                PERMISSIONS.DELETE_SERVICE
                              ].map(permission => (
                                <div key={permission} className="flex items-center space-x-2">
                                  <Checkbox
                                    id={`permission-${permission}`}
                                    checked={newRole.permissions?.includes(permission)}
                                    onCheckedChange={(checked) => {
                                      if (checked) {
                                        setNewRole({
                                          ...newRole,
                                          permissions: [...(newRole.permissions || []), permission]
                                        })
                                      } else {
                                        setNewRole({
                                          ...newRole,
                                          permissions: newRole.permissions?.filter(p => p !== permission) || []
                                        })
                                      }
                                    }}
                                  />
                                  <Label htmlFor={`permission-${permission}`} className="text-sm font-normal">
                                    {permission.split('_').map(word => word.charAt(0).toUpperCase() + word.slice(1)).join(' ')}
                                  </Label>
                                </div>
                              ))}
                            </div>
                          </div>

                          {/* Inventory Permissions */}
                          <div className="space-y-2">
                            <h4 className="text-sm font-medium mb-2">Inventory Management</h4>
                            <div className="grid grid-cols-2 gap-2">
                              {[
                                PERMISSIONS.VIEW_INVENTORY,
                                PERMISSIONS.CREATE_INVENTORY,
                                PERMISSIONS.EDIT_INVENTORY,
                                PERMISSIONS.DELETE_INVENTORY
                              ].map(permission => (
                                <div key={permission} className="flex items-center space-x-2">
                                  <Checkbox
                                    id={`permission-${permission}`}
                                    checked={newRole.permissions?.includes(permission)}
                                    onCheckedChange={(checked) => {
                                      if (checked) {
                                        setNewRole({
                                          ...newRole,
                                          permissions: [...(newRole.permissions || []), permission]
                                        })
                                      } else {
                                        setNewRole({
                                          ...newRole,
                                          permissions: newRole.permissions?.filter(p => p !== permission) || []
                                        })
                                      }
                                    }}
                                  />
                                  <Label htmlFor={`permission-${permission}`} className="text-sm font-normal">
                                    {permission.split('_').map(word => word.charAt(0).toUpperCase() + word.slice(1)).join(' ')}
                                  </Label>
                                </div>
                              ))}
                            </div>
                          </div>

                          {/* POS Permissions */}
                          <div className="space-y-2">
                            <h4 className="text-sm font-medium mb-2">Point of Sale</h4>
                            <div className="grid grid-cols-2 gap-2">
                              {[
                                PERMISSIONS.VIEW_POS,
                                PERMISSIONS.CREATE_SALE,
                                PERMISSIONS.EDIT_SALE,
                                PERMISSIONS.DELETE_SALE,
                                PERMISSIONS.APPLY_DISCOUNT,
                                PERMISSIONS.ISSUE_REFUND
                              ].map(permission => (
                                <div key={permission} className="flex items-center space-x-2">
                                  <Checkbox
                                    id={`permission-${permission}`}
                                    checked={newRole.permissions?.includes(permission)}
                                    onCheckedChange={(checked) => {
                                      if (checked) {
                                        setNewRole({
                                          ...newRole,
                                          permissions: [...(newRole.permissions || []), permission]
                                        })
                                      } else {
                                        setNewRole({
                                          ...newRole,
                                          permissions: newRole.permissions?.filter(p => p !== permission) || []
                                        })
                                      }
                                    }}
                                  />
                                  <Label htmlFor={`permission-${permission}`} className="text-sm font-normal">
                                    {permission.split('_').map(word => word.charAt(0).toUpperCase() + word.slice(1)).join(' ')}
                                  </Label>
                                </div>
                              ))}
                            </div>
                          </div>

                          {/* Other Permissions */}
                          <div className="space-y-2">
                            <h4 className="text-sm font-medium mb-2">Other Permissions</h4>
                            <div className="grid grid-cols-2 gap-2">
                              {[
                                PERMISSIONS.VIEW_ACCOUNTING,
                                PERMISSIONS.MANAGE_ACCOUNTING,
                                PERMISSIONS.VIEW_HR,
                                PERMISSIONS.MANAGE_HR,
                                PERMISSIONS.VIEW_REPORTS,
                                PERMISSIONS.EXPORT_REPORTS,
                                PERMISSIONS.VIEW_SETTINGS,
                                PERMISSIONS.EDIT_SETTINGS,
                                PERMISSIONS.MANAGE_LOCATIONS
                              ].map(permission => (
                                <div key={permission} className="flex items-center space-x-2">
                                  <Checkbox
                                    id={`permission-${permission}`}
                                    checked={newRole.permissions?.includes(permission)}
                                    onCheckedChange={(checked) => {
                                      if (checked) {
                                        setNewRole({
                                          ...newRole,
                                          permissions: [...(newRole.permissions || []), permission]
                                        })
                                      } else {
                                        setNewRole({
                                          ...newRole,
                                          permissions: newRole.permissions?.filter(p => p !== permission) || []
                                        })
                                      }
                                    }}
                                  />
                                  <Label htmlFor={`permission-${permission}`} className="text-sm font-normal">
                                    {permission.split('_').map(word => word.charAt(0).toUpperCase() + word.slice(1)).join(' ')}
                                  </Label>
                                </div>
                              ))}
                            </div>
                          </div>
                        </>
                      )}
                    </div>
                  </div>
                </div>
                <DialogFooter>
                  <Button variant="outline" onClick={() => {
                    setIsAddRoleDialogOpen(false)
                    setNewRole({
                      name: "",
                      description: "",
                      permissions: [],
                    })
                  }}>Cancel</Button>
                  <Button
                    onClick={() => {
                      setIsSubmitting(true)

                      try {
                        // Generate a unique ID
                        const newId = `role_${Date.now()}`

                        // Create the new role object
                        const roleToAdd: Role = {
                          id: newId,
                          name: newRole.name as string,
                          description: newRole.description as string,
                          permissions: newRole.permissions as string[],
                          userCount: 0,
                        }

                        // Add to roles array
                        const updatedRoles = [...roles, roleToAdd]
                        setRoles(updatedRoles)

                        // Save to storage
                        SettingsStorage.saveRoles(updatedRoles)

                        // Reset form and close dialog
                        setNewRole({
                          name: "",
                          description: "",
                          permissions: [],
                        })
                        setIsAddRoleDialogOpen(false)

                        toast({
                          title: "Role added",
                          description: "New role has been added successfully.",
                        })
                      } catch (error) {
                        console.error("Failed to add role:", error)
                        toast({
                          variant: "destructive",
                          title: "Error",
                          description: "Failed to add role. Please try again.",
                        })
                      } finally {
                        setIsSubmitting(false)
                      }
                    }}
                    disabled={isSubmitting || !newRole.name || !newRole.description || newRole.permissions?.length === 0}
                  >
                    {isSubmitting ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Adding...
                      </>
                    ) : (
                      "Add Role"
                    )}
                  </Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>
          </div>

          <Card>
            <CardContent className="p-0">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Role Name</TableHead>
                    <TableHead>Description</TableHead>
                    <TableHead>Users</TableHead>
                    <TableHead>Permissions</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {roles.map((role) => (
                    <TableRow key={role.id}>
                      <TableCell className="font-medium">{role.name}</TableCell>
                      <TableCell>{role.description}</TableCell>
                      <TableCell>{role.userCount}</TableCell>
                      <TableCell>
                        <div className="flex flex-wrap gap-1">
                          {role.permissions.length === 1 && role.permissions[0] === "all" ? (
                            <Badge variant="default">All Permissions</Badge>
                          ) : (
                            role.permissions.slice(0, 2).map((permission) => (
                              <Badge key={permission} variant="outline" className="text-xs">
                                {permission
                                  .split("_")
                                  .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
                                  .join(" ")}
                              </Badge>
                            ))
                          )}
                          {role.permissions.length > 2 && (
                            <Badge variant="outline" className="text-xs">
                              +{role.permissions.length - 2} more
                            </Badge>
                          )}
                        </div>
                      </TableCell>
                      <TableCell className="text-right">
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" size="icon">
                              <MoreHorizontal className="h-4 w-4" />
                              <span className="sr-only">Open menu</span>
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuItem onClick={() => {
                              setRoleToEdit(role)
                              setNewRole({
                                name: role.name,
                                description: role.description,
                                permissions: [...role.permissions],
                              })
                              setIsEditRoleDialogOpen(true)
                            }}>
                              <Edit className="mr-2 h-4 w-4" />
                              Edit role
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={() => {
                              setRoleToEdit(role)
                              setNewRole({
                                name: role.name,
                                description: role.description,
                                permissions: [...role.permissions],
                              })
                              setIsEditRoleDialogOpen(true)
                            }}>
                              View permissions
                            </DropdownMenuItem>
                            <DropdownMenuItem>View users</DropdownMenuItem>
                            {role.id !== "super_admin" && role.id !== "staff" && (
                              <DropdownMenuItem
                                className="text-destructive focus:text-destructive"
                                onClick={() => {
                                  setRoleToDelete(role.id)
                                  setIsDeleteRoleDialogOpen(true)
                                }}
                              >
                                <Trash className="mr-2 h-4 w-4" />
                                Delete role
                              </DropdownMenuItem>
                            )}
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>

          {/* Edit Role Dialog */}
          <Dialog open={isEditRoleDialogOpen} onOpenChange={setIsEditRoleDialogOpen}>
            <DialogContent className="sm:max-w-[600px]">
              <DialogHeader>
                <DialogTitle>Edit Role</DialogTitle>
                <DialogDescription>
                  Update role information and permissions.
                </DialogDescription>
              </DialogHeader>
              {roleToEdit && (
                <div className="grid gap-4 py-4">
                  <div className="grid grid-cols-1 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="edit-role-name">Role Name</Label>
                      <Input
                        id="edit-role-name"
                        value={newRole.name as string}
                        onChange={(e) => setNewRole({...newRole, name: e.target.value})}
                        required
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="edit-role-description">Description</Label>
                      <Input
                        id="edit-role-description"
                        value={newRole.description as string}
                        onChange={(e) => setNewRole({...newRole, description: e.target.value})}
                        required
                      />
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label>Permissions</Label>
                    <div className="border rounded-md p-4 space-y-4">
                      <div className="space-y-2">
                        <div className="flex items-center space-x-2">
                          <Checkbox
                            id="edit-permission-all"
                            checked={newRole.permissions?.includes("all")}
                            onCheckedChange={(checked) => {
                              if (checked) {
                                setNewRole({...newRole, permissions: ["all"]})
                              } else {
                                setNewRole({...newRole, permissions: []})
                              }
                            }}
                          />
                          <Label htmlFor="edit-permission-all" className="font-medium">
                            All Permissions (Super Admin)
                          </Label>
                        </div>
                      </div>

                      {!newRole.permissions?.includes("all") && (
                        <>
                          <div className="space-y-2">
                            <h4 className="text-sm font-medium mb-2">User Management</h4>
                            <div className="grid grid-cols-2 gap-2">
                              {["manage_staff", "view_staff"].map(permission => (
                                <div key={permission} className="flex items-center space-x-2">
                                  <Checkbox
                                    id={`edit-permission-${permission}`}
                                    checked={newRole.permissions?.includes(permission)}
                                    onCheckedChange={(checked) => {
                                      if (checked) {
                                        setNewRole({
                                          ...newRole,
                                          permissions: [...(newRole.permissions || []), permission]
                                        })
                                      } else {
                                        setNewRole({
                                          ...newRole,
                                          permissions: newRole.permissions?.filter(p => p !== permission) || []
                                        })
                                      }
                                    }}
                                  />
                                  <Label htmlFor={`edit-permission-${permission}`} className="text-sm font-normal">
                                    {permission.split("_").map(word => word.charAt(0).toUpperCase() + word.slice(1)).join(" ")}
                                  </Label>
                                </div>
                              ))}
                            </div>
                          </div>

                          <div className="space-y-2">
                            <h4 className="text-sm font-medium mb-2">Client Management</h4>
                            <div className="grid grid-cols-2 gap-2">
                              {["manage_clients", "view_clients"].map(permission => (
                                <div key={permission} className="flex items-center space-x-2">
                                  <Checkbox
                                    id={`edit-permission-${permission}`}
                                    checked={newRole.permissions?.includes(permission)}
                                    onCheckedChange={(checked) => {
                                      if (checked) {
                                        setNewRole({
                                          ...newRole,
                                          permissions: [...(newRole.permissions || []), permission]
                                        })
                                      } else {
                                        setNewRole({
                                          ...newRole,
                                          permissions: newRole.permissions?.filter(p => p !== permission) || []
                                        })
                                      }
                                    }}
                                  />
                                  <Label htmlFor={`edit-permission-${permission}`} className="text-sm font-normal">
                                    {permission.split("_").map(word => word.charAt(0).toUpperCase() + word.slice(1)).join(" ")}
                                  </Label>
                                </div>
                              ))}
                            </div>
                          </div>

                          <div className="space-y-2">
                            <h4 className="text-sm font-medium mb-2">Appointment Management</h4>
                            <div className="grid grid-cols-2 gap-2">
                              {["view_appointments", "manage_appointments", "manage_own_appointments"].map(permission => (
                                <div key={permission} className="flex items-center space-x-2">
                                  <Checkbox
                                    id={`edit-permission-${permission}`}
                                    checked={newRole.permissions?.includes(permission)}
                                    onCheckedChange={(checked) => {
                                      if (checked) {
                                        setNewRole({
                                          ...newRole,
                                          permissions: [...(newRole.permissions || []), permission]
                                        })
                                      } else {
                                        setNewRole({
                                          ...newRole,
                                          permissions: newRole.permissions?.filter(p => p !== permission) || []
                                        })
                                      }
                                    }}
                                  />
                                  <Label htmlFor={`edit-permission-${permission}`} className="text-sm font-normal">
                                    {permission.split("_").map(word => word.charAt(0).toUpperCase() + word.slice(1)).join(" ")}
                                  </Label>
                                </div>
                              ))}
                            </div>
                          </div>

                          <div className="space-y-2">
                            <h4 className="text-sm font-medium mb-2">Other Permissions</h4>
                            <div className="grid grid-cols-2 gap-2">
                              {[
                                "manage_locations",
                                "manage_services",
                                "view_services",
                                "manage_inventory",
                                "view_inventory",
                                "manage_reports",
                                "view_reports",
                                "use_pos"
                              ].map(permission => (
                                <div key={permission} className="flex items-center space-x-2">
                                  <Checkbox
                                    id={`edit-permission-${permission}`}
                                    checked={newRole.permissions?.includes(permission)}
                                    onCheckedChange={(checked) => {
                                      if (checked) {
                                        setNewRole({
                                          ...newRole,
                                          permissions: [...(newRole.permissions || []), permission]
                                        })
                                      } else {
                                        setNewRole({
                                          ...newRole,
                                          permissions: newRole.permissions?.filter(p => p !== permission) || []
                                        })
                                      }
                                    }}
                                  />
                                  <Label htmlFor={`edit-permission-${permission}`} className="text-sm font-normal">
                                    {permission.split("_").map(word => word.charAt(0).toUpperCase() + word.slice(1)).join(" ")}
                                  </Label>
                                </div>
                              ))}
                            </div>
                          </div>
                        </>
                      )}
                    </div>
                  </div>
                </div>
              )}
              <DialogFooter>
                <Button variant="outline" onClick={() => {
                  setIsEditRoleDialogOpen(false)
                  setRoleToEdit(null)
                  setNewRole({
                    name: "",
                    description: "",
                    permissions: [],
                  })
                }}>Cancel</Button>
                <Button
                  onClick={() => {
                    setIsSubmitting(true)

                    try {
                      if (!roleToEdit) return;

                      // Create the updated role object
                      const updatedRole: Role = {
                        ...roleToEdit,
                        name: newRole.name as string,
                        description: newRole.description as string,
                        permissions: newRole.permissions as string[],
                      }

                      // Update the roles array
                      const updatedRoles = roles.map(role =>
                        role.id === updatedRole.id ? updatedRole : role
                      )
                      setRoles(updatedRoles)

                      // Save to storage
                      SettingsStorage.saveRoles(updatedRoles)

                      // Reset form and close dialog
                      setNewRole({
                        name: "",
                        description: "",
                        permissions: [],
                      })
                      setRoleToEdit(null)
                      setIsEditRoleDialogOpen(false)

                      toast({
                        title: "Role updated",
                        description: "Role has been updated successfully.",
                      })
                    } catch (error) {
                      console.error("Failed to update role:", error)
                      toast({
                        variant: "destructive",
                        title: "Error",
                        description: "Failed to update role. Please try again.",
                      })
                    } finally {
                      setIsSubmitting(false)
                    }
                  }}
                  disabled={isSubmitting || !roleToEdit || !newRole.name || !newRole.description || newRole.permissions?.length === 0}
                >
                  {isSubmitting ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Updating...
                    </>
                  ) : (
                    "Update Role"
                  )}
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>

          {/* Delete Role Confirmation Dialog */}
          <AlertDialog open={isDeleteRoleDialogOpen} onOpenChange={setIsDeleteRoleDialogOpen}>
            <AlertDialogContent>
              <AlertDialogHeader>
                <AlertDialogTitle>Are you sure?</AlertDialogTitle>
                <AlertDialogDescription>
                  This action cannot be undone. This will permanently delete the role.
                  Users with this role will be reassigned to the default "Staff" role.
                </AlertDialogDescription>
              </AlertDialogHeader>
              <AlertDialogFooter>
                <AlertDialogCancel onClick={() => setRoleToDelete(null)}>Cancel</AlertDialogCancel>
                <AlertDialogAction
                  onClick={() => {
                    if (!roleToDelete) return;

                    try {
                      // Remove from roles array
                      const updatedRoles = roles.filter(role => role.id !== roleToDelete)
                      setRoles(updatedRoles)

                      // Reassign users with this role to the default "Staff" role
                      const updatedUsers = users.map(user => {
                        if (user.role === roleToDelete) {
                          return { ...user, role: "staff" }
                        }
                        return user
                      })
                      setUsers(updatedUsers)

                      // Save to storage
                      SettingsStorage.saveRoles(updatedRoles)
                      SettingsStorage.saveUsers(updatedUsers)

                      // Reset and close dialog
                      setRoleToDelete(null)
                      setIsDeleteRoleDialogOpen(false)

                      toast({
                        title: "Role deleted",
                        description: "Role has been deleted successfully and users reassigned.",
                      })
                    } catch (error) {
                      console.error("Failed to delete role:", error)
                      toast({
                        variant: "destructive",
                        title: "Error",
                        description: "Failed to delete role. Please try again.",
                      })
                    }
                  }}
                  className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                >
                  Delete
                </AlertDialogAction>
              </AlertDialogFooter>
            </AlertDialogContent>
          </AlertDialog>
        </TabsContent>
      </Tabs>
    </div>
  )
}

