"use client"

import { useState, useEffect } from "react"
import { Card } from "@/components/ui/card"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { CurrencyDisplay } from "@/components/ui/currency-display"
import type { DateRange } from "react-day-picker"
import {
  Eye,
  MoreHorizontal,
  Calendar,
  ShoppingCart,
  Edit,
  Package,
  Globe,
  FileText
} from "lucide-react"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { useTransactions } from "@/lib/transaction-provider"
import {
  Transaction,
  TransactionSource,
  TransactionStatus
} from "@/lib/transaction-types"
import { format } from "date-fns"
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"

interface TransactionsProps {
  search: string
  dateRange?: DateRange
  singleDate?: Date
  selectedLocation?: string
  dateMode?: "single" | "range"
}

export function Transactions({
  search,
  dateRange,
  singleDate,
  selectedLocation = "all",
  dateMode = "range"
}: TransactionsProps) {
  const { transactions, filterTransactions } = useTransactions()
  const [filteredTransactions, setFilteredTransactions] = useState<Transaction[]>([])

  // Apply filters whenever inputs change
  useEffect(() => {
    const filters: any = { search }

    if (selectedLocation !== "all") {
      filters.location = selectedLocation
    }

    if (dateMode === "range" && dateRange?.from) {
      filters.startDate = dateRange.from
      filters.endDate = dateRange.to || dateRange.from
    } else if (dateMode === "single" && singleDate) {
      filters.singleDate = singleDate
    }

    setFilteredTransactions(filterTransactions(filters))
  }, [search, dateRange, singleDate, selectedLocation, dateMode, filterTransactions])

  // Helper function to get source icon
  const getSourceIcon = (source: TransactionSource) => {
    switch (source) {
      case TransactionSource.POS:
        return <ShoppingCart className="h-4 w-4" />
      case TransactionSource.CALENDAR:
        return <Calendar className="h-4 w-4" />
      case TransactionSource.MANUAL:
        return <Edit className="h-4 w-4" />
      case TransactionSource.INVENTORY:
        return <Package className="h-4 w-4" />
      case TransactionSource.ONLINE:
        return <Globe className="h-4 w-4" />
      default:
        return <FileText className="h-4 w-4" />
    }
  }

  // Helper function to get source label
  const getSourceLabel = (source: TransactionSource) => {
    switch (source) {
      case TransactionSource.POS:
        return "Point of Sale"
      case TransactionSource.CALENDAR:
        return "Appointment"
      case TransactionSource.MANUAL:
        return "Manual Entry"
      case TransactionSource.INVENTORY:
        return "Inventory"
      case TransactionSource.ONLINE:
        return "Online Store"
      default:
        return "System"
    }
  }

  // Helper function to get status badge variant
  const getStatusVariant = (status: TransactionStatus) => {
    switch (status) {
      case TransactionStatus.COMPLETED:
        return "success"
      case TransactionStatus.PENDING:
        return "outline"
      case TransactionStatus.CANCELLED:
        return "destructive"
      case TransactionStatus.REFUNDED:
        return "secondary"
      case TransactionStatus.PARTIAL:
        return "warning"
      default:
        return "default"
    }
  }

  // Helper function to format location name
  const getLocationName = (locationId: string) => {
    switch (locationId) {
      case "loc1":
        return "Downtown Salon"
      case "loc2":
        return "Westside Salon"
      case "loc3":
        return "Northside Salon"
      case "home":
        return "Home Service"
      default:
        return locationId
    }
  }

  return (
    <Card>
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Transaction ID</TableHead>
              <TableHead>Source</TableHead>
              <TableHead>Date</TableHead>
              <TableHead>Client</TableHead>
              <TableHead>Type</TableHead>
              <TableHead>Description</TableHead>
              <TableHead>Amount</TableHead>
              <TableHead>Payment Method</TableHead>
              <TableHead>Location</TableHead>
              <TableHead>Status</TableHead>
              <TableHead className="text-right">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredTransactions.length === 0 ? (
              <TableRow>
                <TableCell colSpan={11} className="h-24 text-center">
                  No transactions found.
                </TableCell>
              </TableRow>
            ) : (
              filteredTransactions.map((transaction) => (
                <TableRow key={transaction.id}>
                  <TableCell className="font-medium">{transaction.id}</TableCell>
                  <TableCell>
                    <TooltipProvider>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <div className="flex items-center justify-center">
                            {getSourceIcon(transaction.source)}
                          </div>
                        </TooltipTrigger>
                        <TooltipContent>
                          <p>{getSourceLabel(transaction.source)}</p>
                        </TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                  </TableCell>
                  <TableCell>
                    {typeof transaction.date === 'string'
                      ? transaction.date
                      : format(transaction.date, 'MMM d, yyyy')}
                  </TableCell>
                  <TableCell>{transaction.clientName || 'N/A'}</TableCell>
                  <TableCell>{transaction.type}</TableCell>
                  <TableCell>{transaction.description}</TableCell>
                  <TableCell><CurrencyDisplay amount={transaction.amount} /></TableCell>
                  <TableCell>{transaction.paymentMethod}</TableCell>
                  <TableCell>{getLocationName(transaction.location)}</TableCell>
                  <TableCell>
                    <Badge variant={getStatusVariant(transaction.status)}>
                      {transaction.status}
                    </Badge>
                  </TableCell>
                  <TableCell className="text-right">
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="icon">
                          <MoreHorizontal className="h-4 w-4" />
                          <span className="sr-only">Open menu</span>
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem>
                          <Eye className="mr-2 h-4 w-4" />
                          View details
                        </DropdownMenuItem>
                        <DropdownMenuItem>Print receipt</DropdownMenuItem>
                        <DropdownMenuItem>Export to PDF</DropdownMenuItem>
                        {transaction.reference && (
                          <DropdownMenuItem>
                            {transaction.source === TransactionSource.CALENDAR ? (
                              <>
                                <Calendar className="mr-2 h-4 w-4" />
                                View appointment
                              </>
                            ) : transaction.source === TransactionSource.POS ? (
                              <>
                                <ShoppingCart className="mr-2 h-4 w-4" />
                                View sale
                              </>
                            ) : transaction.source === TransactionSource.INVENTORY ? (
                              <>
                                <Package className="mr-2 h-4 w-4" />
                                View inventory
                              </>
                            ) : (
                              <>
                                <FileText className="mr-2 h-4 w-4" />
                                View reference
                              </>
                            )}
                          </DropdownMenuItem>
                        )}
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </div>
    </Card>
  )
}

