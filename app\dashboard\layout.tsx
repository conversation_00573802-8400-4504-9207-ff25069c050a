"use client"

import type React from "react"

import { useEffect } from "react"
import { useRouter } from "next/navigation"
import Link from "next/link"
import Image from "next/image"
import { useAuth } from "@/lib/auth-provider"
import { LocationSelector } from "@/components/location-selector"
import { UserNav } from "@/components/user-nav"
import { ModeToggle } from "@/components/mode-toggle"
import { ProtectedNav } from "@/components/protected-nav"
import { Search } from "@/components/search"
import { Notifications } from "@/components/notifications"
import { ChatNotifications } from "@/components/chat/chat-notifications"
import { ChatInterface } from "@/components/chat/chat-interface"

export default function DashboardLayout({
  children,
}: {
  children: React.ReactNode
}) {
  const { user, isAuthenticated, hasPermission } = useAuth()
  const router = useRouter()

  useEffect(() => {
    if (!isAuthenticated) {
      router.push("/login")
    }
  }, [isAuthenticated, router])

  if (!isAuthenticated) {
    return null
  }

  return (
    <div className="flex min-h-screen flex-col">
      <header className="sticky top-0 z-50 flex h-16 items-center border-b bg-background px-4 md:px-6">
        <div className="flex items-center gap-4 md:gap-6">
          {user?.role === "staff" || user?.role === "receptionist" ? (
            // Staff and receptionists - logo links to appointments page
            <Link href="/dashboard/appointments" className="hidden items-center gap-2 md:flex">
              <div className="flex h-8 w-8 items-center justify-center rounded-md bg-primary">
                <Image
                  src="https://hebbkx1anhila5yf.public.blob.vercel-storage.com/Vanity%20Logo1.JPG-Cfmt1BqwuLzS5jwYzV7FvpTY9rW1w4.jpeg"
                  alt="SalonHub Logo"
                  width={20}
                  height={20}
                  className="invert"
                />
              </div>
              <span className="text-xl font-bold">SalonHub</span>
            </Link>
          ) : hasPermission("view_dashboard") ? (
            // Admins and managers with dashboard access - logo links to dashboard
            <Link href="/dashboard" className="hidden items-center gap-2 md:flex">
              <div className="flex h-8 w-8 items-center justify-center rounded-md bg-primary">
                <Image
                  src="https://hebbkx1anhila5yf.public.blob.vercel-storage.com/Vanity%20Logo1.JPG-Cfmt1BqwuLzS5jwYzV7FvpTY9rW1w4.jpeg"
                  alt="SalonHub Logo"
                  width={20}
                  height={20}
                  className="invert"
                />
              </div>
              <span className="text-xl font-bold">SalonHub</span>
            </Link>
          ) : (
            // Users without dashboard access - non-clickable logo
            <div className="hidden items-center gap-2 md:flex">
              <div className="flex h-8 w-8 items-center justify-center rounded-md bg-primary">
                <Image
                  src="https://hebbkx1anhila5yf.public.blob.vercel-storage.com/Vanity%20Logo1.JPG-Cfmt1BqwuLzS5jwYzV7FvpTY9rW1w4.jpeg"
                  alt="SalonHub Logo"
                  width={20}
                  height={20}
                  className="invert"
                />
              </div>
              <span className="text-xl font-bold">SalonHub</span>
            </div>
          )}
          <LocationSelector />
        </div>
        <ProtectedNav className="mx-6" />
        <div className="ml-auto flex items-center gap-4">
          <Search />
          <Notifications />
          <ChatNotifications />
          <ModeToggle />
          <UserNav />
        </div>
      </header>
      <div className="flex flex-1">
        <main className="flex-1 p-4 md:p-6">{children}</main>
      </div>

      {/* Chat Interface */}
      <ChatInterface />
    </div>
  )
}

