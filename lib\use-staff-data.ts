'use client';

import { useState, useEffect, useCallback } from 'react';
import { StaffMember } from '@/lib/staff-storage';
import { useAuth } from '@/lib/auth-provider';

interface UseStaffDataOptions {
  /** Whether to automatically fetch data on mount */
  autoFetch?: boolean;
  /** Dependencies that should trigger a refetch when changed */
  dependencies?: any[];
}

interface UseStaffDataResult {
  /** All staff members */
  staff: StaffMember[];
  /** Staff members filtered by the current location */
  filteredStaff: StaffMember[];
  /** Whether the data is currently loading */
  isLoading: boolean;
  /** Any error that occurred during data fetching */
  error: Error | null;
  /** Function to manually refresh the data */
  refreshStaff: () => Promise<void>;
}

/**
 * Hook for fetching staff data with automatic revalidation
 * 
 * This hook provides a way to fetch staff data with automatic revalidation
 * when staff data changes.
 */
export function useStaffData(options: UseStaffDataOptions = {}): UseStaffDataResult {
  const { autoFetch = true, dependencies = [] } = options;
  const { currentLocation } = useAuth();
  const [staff, setStaff] = useState<StaffMember[]>([]);
  const [filteredStaff, setFilteredStaff] = useState<StaffMember[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);

  // Function to fetch staff data
  const fetchStaff = useCallback(async () => {
    setIsLoading(true);
    setError(null);

    try {
      // Fetch staff data from localStorage
      // In a real application, this would be an API call
      const response = await fetch('/api/staff');
      
      if (!response.ok) {
        throw new Error(`Failed to fetch staff: ${response.statusText}`);
      }
      
      const data = await response.json();
      setStaff(data.staff);
      
      // Filter staff by location
      if (currentLocation === 'all') {
        setFilteredStaff(data.staff);
      } else if (currentLocation === 'home') {
        setFilteredStaff(data.staff.filter((s: StaffMember) => 
          s.homeService === true || s.locations.includes('home')
        ));
      } else {
        setFilteredStaff(data.staff.filter((s: StaffMember) => 
          s.locations.includes(currentLocation)
        ));
      }
    } catch (err) {
      console.error('Error fetching staff data:', err);
      setError(err instanceof Error ? err : new Error('Failed to fetch staff data'));
      
      // Fallback to localStorage if API fails
      try {
        const storedStaff = JSON.parse(localStorage.getItem('vanity_staff') || '[]');
        setStaff(storedStaff);
        
        // Filter staff by location
        if (currentLocation === 'all') {
          setFilteredStaff(storedStaff);
        } else if (currentLocation === 'home') {
          setFilteredStaff(storedStaff.filter((s: StaffMember) => 
            s.homeService === true || s.locations.includes('home')
          ));
        } else {
          setFilteredStaff(storedStaff.filter((s: StaffMember) => 
            s.locations.includes(currentLocation)
          ));
        }
      } catch (localStorageErr) {
        console.error('Error fetching staff from localStorage:', localStorageErr);
      }
    } finally {
      setIsLoading(false);
    }
  }, [currentLocation]);

  // Fetch staff data on mount if autoFetch is true
  useEffect(() => {
    if (autoFetch) {
      fetchStaff();
    }
  }, [fetchStaff, autoFetch, ...dependencies]);

  // Listen for staff-updated events
  useEffect(() => {
    const handleStaffUpdated = () => {
      console.log('Staff updated event received, refreshing staff data');
      fetchStaff();
    };

    // Add event listener
    if (typeof window !== 'undefined') {
      window.addEventListener('staff-updated', handleStaffUpdated);
    }

    // Clean up event listener
    return () => {
      if (typeof window !== 'undefined') {
        window.removeEventListener('staff-updated', handleStaffUpdated);
      }
    };
  }, [fetchStaff]);

  // Listen for data-cache-revalidated events
  useEffect(() => {
    const handleCacheRevalidated = (event: Event) => {
      const customEvent = event as CustomEvent;
      if (customEvent.detail?.tags?.includes('data:staff')) {
        console.log('Staff cache revalidated, refreshing staff data');
        fetchStaff();
      }
    };

    document.addEventListener('data-cache-revalidated', handleCacheRevalidated);

    return () => {
      document.removeEventListener('data-cache-revalidated', handleCacheRevalidated);
    };
  }, [fetchStaff]);

  return {
    staff,
    filteredStaff,
    isLoading,
    error,
    refreshStaff: fetchStaff
  };
}
