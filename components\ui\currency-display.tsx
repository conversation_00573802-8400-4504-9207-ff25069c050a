"use client"

import React, { useEffect, useState } from "react"
import { useCurrency, useCurrencyEnforcer } from "@/lib/currency-provider"

interface CurrencyDisplayProps {
  /** The amount to display */
  amount: number;

  /** Optional CSS class name */
  className?: string;

  /** Whether to show the currency symbol */
  showSymbol?: boolean;

  /** Whether to use locale-specific formatting */
  useLocaleFormat?: boolean;
}

/**
 * A component to display monetary values with the correct currency formatting
 * This component automatically updates when the global currency changes
 */
export function CurrencyDisplay({
  amount,
  className,
  showSymbol = true,
  useLocaleFormat = true,
}: CurrencyDisplayProps) {
  // Use the currency enforcer to ensure this component updates when currency changes
  const { currency, currencyCode, formatCurrency } = useCurrencyEnforcer();
  const [formattedValue, setFormattedValue] = useState<string>("");

  // Update the formatted value whenever the currency or amount changes
  useEffect(() => {
    // Format the amount based on the current currency
    const formatted = useLocaleFormat
      ? new Intl.NumberFormat(undefined, {
          style: 'currency',
          currency: currencyCode,
          minimumFractionDigits: currency.decimalDigits,
          maximumFractionDigits: currency.decimalDigits,
        }).format(amount)
      : `${showSymbol ? currency.symbol : ''}${amount.toFixed(currency.decimalDigits)}`;

    setFormattedValue(formatted);
  }, [amount, currency, currencyCode, showSymbol, useLocaleFormat]);

  return (
    <span
      className={`${className || ''} currency-display`}
      data-currency-code={currencyCode}
      data-currency-display="true"
      data-amount={amount}
      data-currency-symbol={currency.symbol}
      style={{
        position: 'relative',
        zIndex: 20,
        backgroundColor: 'white',
        backgroundImage: 'none !important',
        boxShadow: '0 0 0 4px white'
      }}
    >
      {formattedValue}
    </span>
  );
}
