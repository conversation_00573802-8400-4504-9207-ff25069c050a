"use client"

import React, { create<PERSON>ontext, useContext, useState, useEffect, useMemo } from "react"
import { mockClients } from "@/lib/mock-data"
import { useToast } from "@/components/ui/use-toast"
import { v4 as uuidv4 } from "uuid"
import { dataCache } from "@/lib/data-cache"

// Define client types
export interface ClientPreferences {
  preferredStylists: string[]
  preferredServices: string[]
  preferredProducts: string[]
  allergies: string[]
  notes: string
}

export interface Client {
  id: string
  name: string
  email: string
  phone: string
  address?: string
  city?: string
  state?: string
  zip?: string
  birthday?: string
  lastVisit?: string
  preferredLocation: string
  locations: string[]
  status: "Active" | "Inactive" | "Pending"
  avatar: string
  segment: "VIP" | "Regular" | "New" | "At Risk"
  totalSpent?: number
  referredBy?: string
  notes?: string
  preferences?: ClientPreferences
  color?: string
}

interface ClientContextType {
  clients: Client[]
  getClient: (id: string) => Client | undefined
  addClient: (client: Omit<Client, "id" | "avatar" | "segment" | "status">) => Client
  updateClient: (id: string, clientData: Partial<Client>) => Client | undefined
  deleteClient: (id: string) => boolean
  updateClientPreferences: (id: string, preferences: ClientPreferences) => Client | undefined
  updateClientSegment: (id: string, segment: Client["segment"]) => Client | undefined
  updateClientStatus: (id: string, status: Client["status"]) => Client | undefined
}

const ClientContext = createContext<ClientContextType>({
  clients: [],
  getClient: () => undefined,
  addClient: () => ({} as Client),
  updateClient: () => undefined,
  deleteClient: () => false,
  updateClientPreferences: () => undefined,
  updateClientSegment: () => undefined,
  updateClientStatus: () => undefined,
})

export function ClientProvider({ children }: { children: React.ReactNode }) {
  const [clients, setClients] = useState<Client[]>([])
  const [isInitialized, setIsInitialized] = useState(false)
  const { toast } = useToast()

  // Initialize with mock data on first load - only once, using data cache
  useEffect(() => {
    if (isInitialized) return;

    try {
      // Use the data cache to get clients efficiently
      const cachedClients = dataCache.getFromLocalStorage<Client[]>(
        "vanity_clients",
        mockClients,
        { expiryTimeMs: 60 * 60 * 1000 } // 1 hour cache
      )

      // Ensure we have a valid array
      setClients(Array.isArray(cachedClients) ? cachedClients : mockClients)
    } catch (error) {
      console.error("Error loading clients:", error)
      // Fallback to mock data if there's an error
      setClients(mockClients)
    }

    setIsInitialized(true)
  }, [isInitialized])

  // Save clients to localStorage with debounce to prevent excessive writes
  useEffect(() => {
    if (!isInitialized || clients.length === 0) return;

    const saveTimeout = setTimeout(() => {
      // Use the data cache to save clients efficiently
      dataCache.saveToLocalStorage("vanity_clients", clients)
    }, 300) // 300ms debounce

    return () => clearTimeout(saveTimeout)
  }, [clients, isInitialized])

  // Get a client by ID
  const getClient = (id: string) => {
    return clients.find(client => client.id === id)
  }

  // Add a new client
  const addClient = (clientData: Omit<Client, "id" | "avatar" | "segment" | "status">) => {
    // Generate initials for avatar
    const nameParts = clientData.name.split(" ")
    const initials = nameParts.length > 1
      ? `${nameParts[0][0]}${nameParts[1][0]}`
      : nameParts[0].substring(0, 2)

    // Create new client with defaults
    const newClient: Client = {
      id: uuidv4(),
      avatar: initials.toUpperCase(),
      segment: "New",
      status: "Active",
      ...clientData,
    }

    setClients(prevClients => [...prevClients, newClient])

    toast({
      title: "Client created",
      description: `${newClient.name} has been added to your client database.`,
    })

    return newClient
  }

  // Update an existing client
  const updateClient = (id: string, clientData: Partial<Client>) => {
    let updatedClient: Client | undefined

    setClients(prevClients => {
      const updatedClients = prevClients.map(client => {
        if (client.id === id) {
          updatedClient = { ...client, ...clientData }
          return updatedClient
        }
        return client
      })

      return updatedClients
    })

    if (updatedClient) {
      toast({
        title: "Client updated",
        description: `${updatedClient.name}'s information has been updated.`,
      })
    }

    return updatedClient
  }

  // Delete a client
  const deleteClient = (id: string) => {
    const clientToDelete = getClient(id)

    if (!clientToDelete) return false

    setClients(prevClients => prevClients.filter(client => client.id !== id))

    toast({
      title: "Client deleted",
      description: `${clientToDelete.name} has been removed from your client database.`,
      variant: "destructive",
    })

    return true
  }

  // Update client preferences
  const updateClientPreferences = (id: string, preferences: ClientPreferences) => {
    return updateClient(id, { preferences })
  }

  // Update client segment
  const updateClientSegment = (id: string, segment: Client["segment"]) => {
    return updateClient(id, { segment })
  }

  // Update client status
  const updateClientStatus = (id: string, status: Client["status"]) => {
    return updateClient(id, { status })
  }

  return (
    <ClientContext.Provider
      value={{
        clients,
        getClient,
        addClient,
        updateClient,
        deleteClient,
        updateClientPreferences,
        updateClientSegment,
        updateClientStatus,
      }}
    >
      {children}
    </ClientContext.Provider>
  )
}

export const useClients = () => useContext(ClientContext)
