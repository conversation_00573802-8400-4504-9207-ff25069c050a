"use client"

import type React from "react"

import { useState } from "react"
import { useAuth } from "@/lib/auth-provider"
import { useClients } from "@/lib/client-provider"
import { But<PERSON> } from "@/components/ui/button"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Textarea } from "@/components/ui/textarea"
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { ClientPreferencesForm } from "./client-preferences-form"
import { useToast } from "@/components/ui/use-toast"

interface EnhancedNewClientDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
}

export function EnhancedNewClientDialog({ open, onOpenChange }: EnhancedNewClientDialogProps) {
  const { currentLocation } = useAuth()
  const { addClient } = useClients()
  const { toast } = useToast()
  const [activeTab, setActiveTab] = useState("basic")

  // Basic info
  const [name, setName] = useState("")
  const [email, setEmail] = useState("")
  const [phone, setPhone] = useState("")
  const [address, setAddress] = useState("")
  const [city, setCity] = useState("")
  const [state, setState] = useState("")
  const [zip, setZip] = useState("")
  const [preferredLocation, setPreferredLocation] = useState(currentLocation === "all" ? "loc1" : currentLocation)
  const [birthday, setBirthday] = useState("")
  const [referredBy, setReferredBy] = useState("")
  const [notes, setNotes] = useState("")

  // Preferences
  const [preferences, setPreferences] = useState({
    preferredStylists: [],
    preferredServices: [],
    allergies: [],
    notes: ""
  })

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()

    // Create the new client using the client provider
    addClient({
      name,
      email,
      phone,
      address,
      city,
      state,
      zip,
      preferredLocation,
      birthday,
      referredBy,
      notes,
      locations: [preferredLocation],
      preferences
    })

    onOpenChange(false)

    // Reset form
    setName("")
    setEmail("")
    setPhone("")
    setAddress("")
    setCity("")
    setState("")
    setZip("")
    setPreferredLocation(currentLocation === "all" ? "loc1" : currentLocation)
    setBirthday("")
    setReferredBy("")
    setNotes("")
    setPreferences({
      preferredStylists: [],
      preferredServices: [],
      allergies: [],
      notes: ""
    })
    setActiveTab("basic")
  }

  const handlePreferencesSave = (newPreferences: any) => {
    setPreferences(newPreferences)
    setActiveTab("review")
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px]">
        <form onSubmit={handleSubmit}>
          <DialogHeader>
            <DialogTitle>New Client</DialogTitle>
            <DialogDescription>Add a new client to your salon database.</DialogDescription>
          </DialogHeader>

          <Tabs value={activeTab} onValueChange={setActiveTab} className="mt-5">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="basic">Basic Info</TabsTrigger>
              <TabsTrigger value="preferences">Preferences</TabsTrigger>
              <TabsTrigger value="review">Review</TabsTrigger>
            </TabsList>

            <TabsContent value="basic" className="py-4">
              <div className="grid gap-4">
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="name" className="text-right">
                    Name
                  </Label>
                  <Input
                    id="name"
                    value={name}
                    onChange={(e) => setName(e.target.value)}
                    className="col-span-3"
                    required
                  />
                </div>

                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="email" className="text-right">
                    Email
                  </Label>
                  <Input
                    id="email"
                    type="email"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    className="col-span-3"
                  />
                </div>

                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="phone" className="text-right">
                    Phone
                  </Label>
                  <Input
                    id="phone"
                    type="tel"
                    value={phone}
                    onChange={(e) => setPhone(e.target.value)}
                    className="col-span-3"
                    required
                  />
                </div>

                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="address" className="text-right">
                    Address
                  </Label>
                  <Input
                    id="address"
                    value={address}
                    onChange={(e) => setAddress(e.target.value)}
                    className="col-span-3"
                  />
                </div>

                <div className="grid grid-cols-4 items-center gap-4">
                  <Label className="text-right">
                    City/State/Zip
                  </Label>
                  <div className="col-span-3 grid grid-cols-7 gap-2">
                    <Input
                      id="city"
                      placeholder="City"
                      value={city}
                      onChange={(e) => setCity(e.target.value)}
                      className="col-span-3"
                    />
                    <Input
                      id="state"
                      placeholder="State"
                      value={state}
                      onChange={(e) => setState(e.target.value)}
                      className="col-span-2"
                    />
                    <Input
                      id="zip"
                      placeholder="Zip"
                      value={zip}
                      onChange={(e) => setZip(e.target.value)}
                      className="col-span-2"
                    />
                  </div>
                </div>

                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="birthday" className="text-right">
                    Birthday
                  </Label>
                  <Input
                    id="birthday"
                    type="date"
                    value={birthday}
                    onChange={(e) => setBirthday(e.target.value)}
                    className="col-span-3"
                  />
                </div>

                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="preferredLocation" className="text-right">
                    Preferred Location
                  </Label>
                  <Select
                    value={preferredLocation}
                    onValueChange={setPreferredLocation}
                  >
                    <SelectTrigger id="preferredLocation" className="col-span-3">
                      <SelectValue placeholder="Select location" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="loc1">Downtown</SelectItem>
                      <SelectItem value="loc2">Westside</SelectItem>
                      <SelectItem value="loc3">Northside</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="referredBy" className="text-right">
                    Referred By
                  </Label>
                  <Input
                    id="referredBy"
                    value={referredBy}
                    onChange={(e) => setReferredBy(e.target.value)}
                    className="col-span-3"
                    placeholder="How did they hear about us?"
                  />
                </div>

                <div className="grid grid-cols-4 items-start gap-4">
                  <Label htmlFor="notes" className="text-right pt-2">
                    Notes
                  </Label>
                  <Textarea
                    id="notes"
                    value={notes}
                    onChange={(e) => setNotes(e.target.value)}
                    className="col-span-3"
                    rows={3}
                  />
                </div>

                <div className="flex justify-end mt-2">
                  <Button type="button" onClick={() => setActiveTab("preferences")}>
                    Continue to Preferences
                  </Button>
                </div>
              </div>
            </TabsContent>

            <TabsContent value="preferences" className="py-4">
              <ClientPreferencesForm onSave={handlePreferencesSave} />
            </TabsContent>

            <TabsContent value="review" className="py-4">
              <div className="space-y-6">
                <div>
                  <h3 className="text-lg font-medium">Client Information</h3>
                  <div className="mt-3 grid grid-cols-2 gap-y-2 text-sm">
                    <div className="font-medium">Name:</div>
                    <div>{name || "Not provided"}</div>

                    <div className="font-medium">Email:</div>
                    <div>{email || "Not provided"}</div>

                    <div className="font-medium">Phone:</div>
                    <div>{phone || "Not provided"}</div>

                    <div className="font-medium">Address:</div>
                    <div>
                      {address ? (
                        <>
                          {address}
                          {(city || state || zip) && (
                            <>, {city} {state} {zip}</>
                          )}
                        </>
                      ) : (
                        "Not provided"
                      )}
                    </div>

                    <div className="font-medium">Birthday:</div>
                    <div>{birthday || "Not provided"}</div>

                    <div className="font-medium">Preferred Location:</div>
                    <div>
                      {preferredLocation === "loc1"
                        ? "Downtown"
                        : preferredLocation === "loc2"
                          ? "Westside"
                          : "Northside"}
                    </div>

                    <div className="font-medium">Referred By:</div>
                    <div>{referredBy || "Not provided"}</div>
                  </div>
                </div>

                <div>
                  <h3 className="text-lg font-medium">Client Preferences</h3>
                  <div className="mt-3 grid grid-cols-2 gap-y-2 text-sm">
                    <div className="font-medium">Preferred Stylists:</div>
                    <div>
                      {preferences.preferredStylists.length > 0
                        ? preferences.preferredStylists.join(", ")
                        : "None selected"}
                    </div>

                    <div className="font-medium">Preferred Services:</div>
                    <div>
                      {preferences.preferredServices.length > 0
                        ? preferences.preferredServices.join(", ")
                        : "None selected"}
                    </div>

                    <div className="font-medium">Allergies & Sensitivities:</div>
                    <div>
                      {preferences.allergies.length > 0
                        ? preferences.allergies.join(", ")
                        : "None provided"}
                    </div>

                    <div className="font-medium">Special Instructions:</div>
                    <div>{preferences.notes || "None provided"}</div>
                  </div>
                </div>

                <div className="flex justify-between">
                  <Button type="button" variant="outline" onClick={() => setActiveTab("preferences")}>
                    Back to Preferences
                  </Button>
                  <Button type="submit">
                    Create Client
                  </Button>
                </div>
              </div>
            </TabsContent>
          </Tabs>
        </form>
      </DialogContent>
    </Dialog>
  )
}
