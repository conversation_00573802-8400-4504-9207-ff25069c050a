import { NextResponse } from "next/server"
import { productsRepository } from "@/lib/db"
import { getServerSession } from "next-auth"
import { PERMISSIONS } from "@/lib/permissions"

export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url)
    const locationId = searchParams.get("locationId")

    if (!locationId) {
      return NextResponse.json({ error: "Location ID is required" }, { status: 400 })
    }

    const inventory = await productsRepository.getInventoryByLocation(Number.parseInt(locationId))

    return NextResponse.json({ inventory })
  } catch (error) {
    console.error("Error fetching inventory:", error)
    return NextResponse.json({ error: "Failed to fetch inventory" }, { status: 500 })
  }
}

export async function POST(request: Request) {
  try {
    // Check user session and permissions
    const session = await getServerSession()

    // If no session or user, return unauthorized
    if (!session || !session.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    // Get user permissions from the database
    const { query } = await import("@/lib/db")
    const userResult = await query(
      `SELECT role FROM users WHERE id = $1`,
      [session.user.id]
    )

    if (!userResult.rows.length) {
      return NextResponse.json({ error: "User not found" }, { status: 404 })
    }

    const userRole = userResult.rows[0].role

    // Get role permissions
    const roleResult = await query(
      `SELECT permissions FROM roles WHERE id = $1`,
      [userRole]
    )

    let hasPermission = false

    // Check if user has the required permission
    if (roleResult.rows.length) {
      const permissions = roleResult.rows[0].permissions
      hasPermission = permissions.includes(PERMISSIONS.CREATE_INVENTORY) || permissions.includes(PERMISSIONS.ALL)
    }

    if (!hasPermission) {
      return NextResponse.json({ error: "Permission denied" }, { status: 403 })
    }

    const data = await request.json()

    // Validate required fields
    if (!data.name || !data.locationId) {
      return NextResponse.json({ error: "Missing required fields" }, { status: 400 })
    }

    // Create the product
    const product = await productsRepository.create({
      name: data.name,
      description: data.description || null,
      sku: data.sku || null,
      barcode: data.barcode || null,
      category_id: data.category_id || null,
      retail_price: data.isRetail ? Number.parseFloat(data.retailPrice) : null,
      cost_price: Number.parseFloat(data.costPrice) || 0,
      is_retail: data.isRetail || false,
    })

    // Add initial inventory
    if (product.id) {
      await productsRepository.updateInventory(
        product.id,
        Number.parseInt(data.locationId),
        Number.parseInt(data.initialStock) || 0,
      )

      // Record inventory transaction
      const { query } = await import("@/lib/db")

      await query(
        `INSERT INTO inventory_transactions
          (product_id, location_id, quantity, transaction_type, notes, created_by)
         VALUES ($1, $2, $3, $4, $5, $6)`,
        [
          product.id,
          Number.parseInt(data.locationId),
          Number.parseInt(data.initialStock) || 0,
          "initial",
          "Initial inventory setup",
          data.userId || null,
        ],
      )

      // Update min/max stock levels
      await query(
        `UPDATE inventory
         SET min_stock_level = $1, max_stock_level = $2
         WHERE product_id = $3 AND location_id = $4`,
        [
          Number.parseInt(data.minStockLevel) || 0,
          Number.parseInt(data.maxStockLevel) || 0,
          product.id,
          Number.parseInt(data.locationId),
        ],
      )
    }

    return NextResponse.json({ product })
  } catch (error) {
    console.error("Error creating product:", error)
    return NextResponse.json({ error: "Failed to create product" }, { status: 500 })
  }
}

