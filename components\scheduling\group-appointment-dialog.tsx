"use client"

import { useState, useEffect } from "react"
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  <PERSON>alogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Textarea } from "@/components/ui/textarea"
import { Calendar } from "@/components/ui/calendar"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { CalendarIcon, Plus, X, Users } from "lucide-react"
import { format, set, isBefore, startOfDay, isToday } from "date-fns"
import { cn } from "@/lib/utils"
import { mockServices, mockStaff } from "@/lib/mock-data"
import { useToast } from "@/components/ui/use-toast"
import { <PERSON>roll<PERSON><PERSON> } from "@/components/ui/scroll-area"
import { <PERSON><PERSON> } from "@/components/ui/badge"
import { CurrencyDisplay } from "@/components/ui/currency-display"
import { useCurrency } from "@/lib/currency-provider"

interface GroupAppointmentDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  initialDate?: Date
  initialTime?: string
  initialStaffId?: string
  onAppointmentCreated?: (appointment: any) => void
}

export function GroupAppointmentDialog({
  open,
  onOpenChange,
  initialDate = new Date(),
  initialTime,
  initialStaffId,
  onAppointmentCreated,
}: GroupAppointmentDialogProps) {
  const { toast } = useToast()
  const { currency } = useCurrency()
  const [formData, setFormData] = useState({
    date: initialDate,
    time: initialTime || "10:00",
    serviceId: "",
    staffId: initialStaffId || "",
    location: "loc1",
    notes: "",
    maxParticipants: "5",
  })

  const [clients, setClients] = useState<Array<{ id: string; name: string; email: string; phone: string }>>([
    { id: "1", name: "", email: "", phone: "" }
  ])

  const [isSubmitting, setIsSubmitting] = useState(false)

  // Reset form when dialog opens with new initial date
  useEffect(() => {
    if (open) {
      setFormData({
        ...formData,
        date: initialDate,
        time: initialTime || "10:00",
        staffId: initialStaffId || "",
      })
      setClients([{ id: "1", name: "", email: "", phone: "" }])
    }
  }, [open, initialDate, initialTime, initialStaffId])

  const handleAddClient = () => {
    const newClientId = `client-${Date.now()}`;
    console.log("Group appointment - Adding new client with ID:", newClientId);
    setClients(prevClients => [...prevClients, {
      id: newClientId,
      name: "",
      email: "",
      phone: ""
    }])
  }

  const handleRemoveClient = (id: string) => {
    if (clients.length > 1) {
      console.log("Group appointment - Removing client with ID:", id);
      setClients(prevClients => prevClients.filter(client => client.id !== id))
    }
  }

  const handleClientChange = (id: string, field: string, value: string) => {
    console.log(`Group appointment - Client ${id} ${field} changed:`, value);
    setClients(prevClients =>
      prevClients.map(client =>
        client.id === id ? { ...client, [field]: value } : client
      )
    )
  }

  const handleSubmit = async () => {
    // Validate form
    if (!formData.serviceId || !formData.staffId || clients.some(client => !client.name)) {
      toast({
        variant: "destructive",
        title: "Missing information",
        description: "Please fill out all required fields including client names and service.",
      })
      return
    }

    // Create appointment date by combining date and time
    const [hours, minutes] = formData.time.split(":").map(Number)
    const appointmentDate = set(formData.date, { hours, minutes })

    // Validate that appointment time is in the future
    const now = new Date()
    if (isBefore(appointmentDate, now)) {
      toast({
        variant: "destructive",
        title: "Invalid appointment time",
        description: "Group appointments cannot be scheduled in the past. Please select a future time.",
      })
      return
    }

    setIsSubmitting(true)

    try {
      // Get the selected service details
      const selectedService = mockServices.find((s) => s.id === formData.serviceId)
      const selectedStaff = mockStaff.find((s) => s.id === formData.staffId)

      if (!selectedService || !selectedStaff) {
        throw new Error("Service or staff not found")
      }

      // Create a new group appointment object
      const newGroupAppointment = {
        id: `group-appointment-${Date.now()}`,
        clients: clients.map(client => ({
          clientId: client.id,
          clientName: client.name,
          email: client.email,
          phone: client.phone
        })),
        staffId: formData.staffId,
        staffName: selectedStaff.name,
        service: selectedService.name,
        date: format(appointmentDate, "yyyy-MM-dd'T'HH:mm:ss"),
        duration: selectedService.duration,
        status: "pending",
        location: formData.location,
        notes: formData.notes,
        maxParticipants: parseInt(formData.maxParticipants),
        type: "group"
      }

      // Call the callback with the new appointment
      if (onAppointmentCreated) {
        onAppointmentCreated(newGroupAppointment)
      }

      toast({
        title: "Group appointment created",
        description: `Group appointment for ${clients.length} clients on ${format(appointmentDate, "MMMM d, yyyy")} at ${format(appointmentDate, "h:mm a")} has been scheduled.`,
      })

      // Reset form and close dialog
      onOpenChange(false)
    } catch (error) {
      console.error("Failed to create group appointment:", error)
      toast({
        variant: "destructive",
        title: "Error",
        description: "Failed to create the group appointment. Please try again.",
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader className="flex flex-row items-center justify-between">
          <div>
            <DialogTitle>New Group Appointment</DialogTitle>
            <DialogDescription>Schedule multiple clients for the same service.</DialogDescription>
          </div>
          <Button variant="ghost" size="icon" onClick={() => onOpenChange(false)}>
            <X className="h-4 w-4" />
          </Button>
        </DialogHeader>

        <div className="grid gap-4 py-4">
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="service">Service</Label>
              <Select
                value={formData.serviceId}
                onValueChange={(value) => {
                  console.log("Group appointment - Service selected:", value);
                  setFormData((prevData) => ({
                    ...prevData,
                    serviceId: value
                  }));
                }}
              >
                <SelectTrigger id="service">
                  <SelectValue placeholder="Select a service" />
                </SelectTrigger>
                <SelectContent>
                  {mockServices.map((service) => (
                    <SelectItem key={service.id} value={service.id}>
                      {service.name} - <CurrencyDisplay amount={service.price} showSymbol={true} useLocaleFormat={false} /> ({service.duration} min)
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="staff">Staff Member</Label>
              <Select
                value={formData.staffId}
                onValueChange={(value) => {
                  console.log("Group appointment - Staff selected:", value);
                  setFormData((prevData) => ({
                    ...prevData,
                    staffId: value
                  }));
                }}
              >
                <SelectTrigger id="staff">
                  <SelectValue placeholder="Select staff" />
                </SelectTrigger>
                <SelectContent>
                  {mockStaff
                    .filter((staff) => staff.role === "staff")
                    .map((staff) => (
                      <SelectItem key={staff.id} value={staff.id}>
                        {staff.name}
                      </SelectItem>
                    ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="date">Date</Label>
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    className={cn(
                      "w-full justify-start text-left font-normal",
                      !formData.date && "text-muted-foreground",
                    )}
                  >
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {formData.date ? format(formData.date, "MMMM d, yyyy") : "Select date"}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0">
                  <Calendar
                    mode="single"
                    selected={formData.date}
                    onSelect={(date) => {
                      if (date) {
                        console.log("Group appointment - Date selected:", date);
                        setFormData((prevData) => ({
                          ...prevData,
                          date
                        }));
                      }
                    }}
                    initialFocus
                  />
                </PopoverContent>
              </Popover>
            </div>

            <div className="space-y-2">
              <Label htmlFor="time">Time</Label>
              <Select
                value={formData.time}
                onValueChange={(value) => {
                  console.log("Group appointment - Time selected:", value);
                  setFormData((prevData) => ({
                    ...prevData,
                    time: value
                  }));
                }}
              >
                <SelectTrigger id="time">
                  <SelectValue placeholder="Select time" />
                </SelectTrigger>
                <SelectContent>
                  {Array.from({ length: 15 * 4 }, (_, i) => {
                    const hour = Math.floor(i / 4) + 9; // Start from 9 AM
                    const minute = (i % 4) * 15;
                    const formattedHour = hour % 12 === 0 ? 12 : hour % 12;
                    const period = hour >= 12 ? "PM" : "AM";
                    const timeValue = `${hour.toString().padStart(2, "0")}:${minute.toString().padStart(2, "0")}`;
                    const timeLabel = `${formattedHour}:${minute.toString().padStart(2, "0")} ${period}`;
                    return { value: timeValue, label: timeLabel };
                  }).map((time) => (
                    <SelectItem key={time.value} value={time.value}>
                      {time.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="location">Location</Label>
              <Select
                value={formData.location}
                onValueChange={(value) => {
                  console.log("Group appointment - Location selected:", value);
                  setFormData((prevData) => ({
                    ...prevData,
                    location: value
                  }));
                }}
              >
                <SelectTrigger id="location">
                  <SelectValue placeholder="Select location" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="loc1">Downtown Salon</SelectItem>
                  <SelectItem value="loc2">Westside Salon</SelectItem>
                  <SelectItem value="loc3">Northside Salon</SelectItem>
                  <SelectItem value="loc4">Home Service</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="maxParticipants">Maximum Participants</Label>
              <Select
                value={formData.maxParticipants}
                onValueChange={(value) => {
                  console.log("Group appointment - Max participants selected:", value);
                  setFormData((prevData) => ({
                    ...prevData,
                    maxParticipants: value
                  }));
                }}
              >
                <SelectTrigger id="maxParticipants">
                  <SelectValue placeholder="Select max participants" />
                </SelectTrigger>
                <SelectContent>
                  {[2, 3, 4, 5, 6, 8, 10, 12, 15].map((num) => (
                    <SelectItem key={num} value={num.toString()}>
                      {num} participants
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <Label>Clients</Label>
              <Badge variant="outline" className="font-normal">
                <Users className="h-3 w-3 mr-1" />
                {clients.length} of {formData.maxParticipants}
              </Badge>
            </div>

            <ScrollArea className="h-[200px] rounded-md border p-4">
              <div className="space-y-4">
                {clients.map((client, index) => (
                  <div key={client.id} className="grid grid-cols-[1fr_1fr_auto] gap-2">
                    <Input
                      placeholder="Client name"
                      value={client.name}
                      onChange={(e) => handleClientChange(client.id, "name", e.target.value)}
                      className="col-span-2 sm:col-span-1"
                    />
                    <Input
                      placeholder="Phone number"
                      value={client.phone}
                      onChange={(e) => handleClientChange(client.id, "phone", e.target.value)}
                      className="col-span-2 sm:col-span-1"
                    />
                    <Button
                      variant="ghost"
                      size="icon"
                      onClick={() => handleRemoveClient(client.id)}
                      disabled={clients.length === 1}
                      className="h-10 w-10"
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  </div>
                ))}
              </div>
            </ScrollArea>

            <Button
              type="button"
              variant="outline"
              size="sm"
              className="mt-2"
              onClick={handleAddClient}
              disabled={clients.length >= parseInt(formData.maxParticipants)}
            >
              <Plus className="h-4 w-4 mr-2" />
              Add Client
            </Button>
          </div>

          <div className="space-y-2">
            <Label htmlFor="notes">Notes</Label>
            <Textarea
              id="notes"
              placeholder="Add any special instructions or notes about this group appointment"
              value={formData.notes}
              onChange={(e) => {
                const newValue = e.target.value;
                console.log("Group appointment - Notes changed:", newValue);
                setFormData((prevData) => ({
                  ...prevData,
                  notes: newValue
                }));
              }}
              className="min-h-[80px]"
            />
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Cancel
          </Button>
          <Button onClick={handleSubmit} disabled={isSubmitting}>
            {isSubmitting ? "Creating..." : "Create Group Appointment"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
