"use client"

import { useState, useEffect } from "react"
import { useAuth } from "@/lib/auth-provider"
import { Card } from "@/components/ui/card"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { But<PERSON> } from "@/components/ui/button"
import { Eye, Edit, MoreHorizontal, Trash, Home } from "lucide-react"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { StaffDetailsDialog } from "./staff-details-dialog"
import { EditStaffDialog } from "./edit-staff-dialog"
import { DeleteStaffDialog } from "./delete-staff-dialog"
import { useLocations } from "@/lib/location-provider"
import { useUnifiedStaff } from "@/lib/unified-staff-provider"
import { useStaff } from "@/lib/staff-provider"

interface StaffDirectoryProps {
  search: string
  onStaffUpdated?: (updatedStaff: any) => void
  onStaffDeleted?: (staffId: string) => void
}

export function StaffDirectory({ search, onStaffUpdated, onStaffDeleted }: StaffDirectoryProps) {
  const { currentLocation, hasPermission } = useAuth()
  const { getLocationName } = useLocations()
  const { staff, getStaffByLocation, updateStaffMember, deleteStaffMember, refreshData } = useUnifiedStaff()
  const { refreshStaff } = useStaff() // Keep for backward compatibility
  const [selectedStaff, setSelectedStaff] = useState<any | null>(null)
  const [isDetailsDialogOpen, setIsDetailsDialogOpen] = useState(false)
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false)
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false)

  // Use staff from the unified provider
  const staffList = staff

  // Refresh staff data when component mounts
  useEffect(() => {
    refreshData()
    refreshStaff() // Keep for backward compatibility
  }, [refreshData, refreshStaff])

  // Filter staff based on location permissions and search term
  const filteredStaff = staffList.filter((staff) => {
    // Filter by location
    if (currentLocation !== "all") {
      // For Home service location, include staff with homeService flag OR staff with "home" in their locations
      if (currentLocation === "home") {
        if (staff.homeService !== true && !staff.locations.includes("home")) {
          return false
        }
      }
      // For regular locations, check if staff is assigned to that location
      else if (!staff.locations.includes(currentLocation)) {
        return false
      }
    }

    // Filter by search term
    if (search && !staff.name.toLowerCase().includes(search.toLowerCase())) {
      return false
    }

    return true
  })

  const handleViewDetails = (staff: any) => {
    setSelectedStaff(staff)
    setIsDetailsDialogOpen(true)
  }

  const handleEditStaff = (staff: any) => {
    setSelectedStaff(staff)
    setIsEditDialogOpen(true)
  }

  const handleDeleteStaff = (staff: any) => {
    setSelectedStaff(staff)
    setIsDeleteDialogOpen(true)
  }

  const handleStaffUpdated = (updatedStaff: any) => {
    // Update the staff member using the unified provider
    const result = updateStaffMember(updatedStaff)

    // Refresh the staff list
    refreshData()
    refreshStaff() // Keep for backward compatibility

    // Call the parent's onStaffUpdated callback if provided
    if (onStaffUpdated) {
      onStaffUpdated(updatedStaff)
    }
  }

  const handleStaffDeleted = (staffId: string) => {
    // Delete the staff member using the unified provider
    const result = deleteStaffMember(staffId)

    // Refresh the staff list
    refreshData()
    refreshStaff() // Keep for backward compatibility

    // Call the parent's onStaffDeleted callback if provided
    if (onStaffDeleted) {
      onStaffDeleted(staffId)
    }
  }

  return (
    <Card>
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Name</TableHead>
              <TableHead>Role</TableHead>
              <TableHead>Email</TableHead>
              <TableHead>Phone</TableHead>
              <TableHead>Locations</TableHead>
              <TableHead>Status</TableHead>
              <TableHead className="text-right">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredStaff.length === 0 ? (
              <TableRow>
                <TableCell colSpan={7} className="h-24 text-center">
                  No staff members found.
                </TableCell>
              </TableRow>
            ) : (
              filteredStaff.map((staff) => (
                <TableRow key={staff.id}>
                  <TableCell className="font-medium">{staff.name}</TableCell>
                  <TableCell>
                    <Badge variant="outline">
                      {staff.role === "super_admin"
                        ? "Super Admin"
                        : staff.role === "org_admin"
                          ? "Organization Admin"
                          : staff.role === "location_manager"
                            ? "Location Manager"
                            : staff.role === "stylist"
                              ? "Stylist"
                              : staff.role === "colorist"
                                ? "Colorist"
                                : staff.role === "barber"
                                  ? "Barber"
                                  : staff.role === "nail_technician"
                                    ? "Nail Technician"
                                    : staff.role === "esthetician"
                                      ? "Esthetician"
                                      : "Receptionist"}
                    </Badge>
                    {staff.homeService && (
                      <Badge variant="outline" className="ml-1 flex items-center gap-1">
                        <Home className="h-3 w-3" />
                        <span className="text-xs">Home</span>
                      </Badge>
                    )}
                  </TableCell>
                  <TableCell>{staff.email}</TableCell>
                  <TableCell>{staff.phone}</TableCell>
                  <TableCell>
                    <div className="flex flex-wrap gap-1">
                      {staff.locations.map((loc) => (
                        <Badge key={loc} variant="secondary" className="text-xs">
                          {getLocationName(loc)}
                        </Badge>
                      ))}
                      {staff.locations.includes("all") && (
                        <Badge variant="secondary" className="text-xs">
                          All Locations
                        </Badge>
                      )}
                      {staff.homeService && !staff.locations.includes("home") && (
                        <Badge variant="secondary" className="text-xs flex items-center gap-1">
                          <Home className="h-3 w-3" />
                          Home Service
                        </Badge>
                      )}
                    </div>
                  </TableCell>
                  <TableCell>
                    <Badge variant={staff.status === "Active" ? "success" : "secondary"}>{staff.status}</Badge>
                  </TableCell>
                  <TableCell className="text-right">
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="icon">
                          <MoreHorizontal className="h-4 w-4" />
                          <span className="sr-only">Open menu</span>
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem onClick={() => handleViewDetails(staff)}>
                          <Eye className="mr-2 h-4 w-4" />
                          View details
                        </DropdownMenuItem>
                        {hasPermission("edit_staff") && (
                          <DropdownMenuItem onClick={() => handleEditStaff(staff)}>
                            <Edit className="mr-2 h-4 w-4" />
                            Edit staff
                          </DropdownMenuItem>
                        )}
                        {hasPermission("delete_staff") && (
                          <DropdownMenuItem onClick={() => handleDeleteStaff(staff)} className="text-red-600">
                            <Trash className="mr-2 h-4 w-4" />
                            Delete staff
                          </DropdownMenuItem>
                        )}
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </div>

      {/* Staff Details Dialog */}
      {selectedStaff && isDetailsDialogOpen && (
        <StaffDetailsDialog
          staff={selectedStaff}
          open={isDetailsDialogOpen}
          onOpenChange={setIsDetailsDialogOpen}
          onStaffUpdated={handleStaffUpdated}
          onStaffDeleted={handleStaffDeleted}
        />
      )}

      {/* Edit Staff Dialog */}
      {selectedStaff && isEditDialogOpen && (
        <EditStaffDialog
          staffId={selectedStaff.id}
          open={isEditDialogOpen}
          onOpenChange={setIsEditDialogOpen}
          onStaffUpdated={handleStaffUpdated}
        />
      )}

      {/* Delete Staff Dialog */}
      {selectedStaff && isDeleteDialogOpen && (
        <DeleteStaffDialog
          staff={{ id: selectedStaff.id, name: selectedStaff.name }}
          open={isDeleteDialogOpen}
          onOpenChange={setIsDeleteDialogOpen}
          onStaffDeleted={handleStaffDeleted}
        />
      )}
    </Card>
  )
}
