import { v4 as uuidv4 } from 'uuid'
import { mockStaff } from './mock-data'

// Storage keys
const STORAGE_KEYS = {
  STAFF: 'vanity_staff',
}

// Staff member type
export interface StaffMember {
  id: string
  name: string
  email: string
  phone: string
  role: string
  locations: string[]
  status: string
  avatar: string
  color: string
  homeService: boolean
}

// Helper function to get data from localStorage
function getFromStorage<T>(key: string, defaultValue: T): T {
  if (typeof window === 'undefined') {
    return defaultValue
  }

  try {
    const storedValue = localStorage.getItem(key)
    return storedValue ? JSON.parse(storedValue) : defaultValue
  } catch (error) {
    console.error(`Error retrieving ${key} from localStorage:`, error)
    return defaultValue
  }
}

// Helper function to save data to localStorage
function saveToStorage<T>(key: string, value: T): void {
  if (typeof window === 'undefined') {
    return
  }

  try {
    localStorage.setItem(key, JSON.stringify(value))
  } catch (error) {
    console.error(`Error saving ${key} to localStorage:`, error)
  }
}

// Staff Storage Service
export const StaffStorage = {
  // Get all staff members
  getStaff: (): StaffMember[] => getFromStorage<StaffMember[]>(STORAGE_KEYS.STAFF, mockStaff),

  // Save all staff members
  saveStaff: (staff: StaffMember[]) => saveToStorage(STORAGE_KEYS.STAFF, staff),

  // Add a new staff member
  addStaff: (staff: Omit<StaffMember, 'id'>): StaffMember => {
    const staffList = StaffStorage.getStaff()
    const newStaff = {
      id: `staff-${Date.now()}`,
      ...staff
    }
    staffList.push(newStaff)
    saveToStorage(STORAGE_KEYS.STAFF, staffList)
    return newStaff
  },

  // Update an existing staff member
  updateStaff: (updatedStaff: StaffMember): StaffMember | null => {
    if (!updatedStaff || !updatedStaff.id) {
      console.error('Cannot update staff: Invalid staff data')
      return null
    }

    const staffList = StaffStorage.getStaff()
    const index = staffList.findIndex(staff => staff.id === updatedStaff.id)
    
    if (index !== -1) {
      staffList[index] = updatedStaff
      saveToStorage(STORAGE_KEYS.STAFF, staffList)
      return updatedStaff
    }
    
    console.error(`Cannot update staff: Staff with ID ${updatedStaff.id} not found`)
    return null
  },

  // Delete a staff member
  deleteStaff: (staffId: string): boolean => {
    const staffList = StaffStorage.getStaff()
    const filteredStaff = staffList.filter(staff => staff.id !== staffId)
    
    if (filteredStaff.length < staffList.length) {
      saveToStorage(STORAGE_KEYS.STAFF, filteredStaff)
      return true
    }
    
    console.error(`Cannot delete staff: Staff with ID ${staffId} not found`)
    return false
  },

  // Get a staff member by ID
  getStaffById: (staffId: string): StaffMember | undefined => {
    const staffList = StaffStorage.getStaff()
    return staffList.find(staff => staff.id === staffId)
  },
}
