"use client"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Ta<PERSON>, <PERSON>bsContent, <PERSON>bsList, TabsTrigger } from "@/components/ui/tabs"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { useToast } from "@/components/ui/use-toast"
import { Star, Gift, Award, Plus, Edit, Trash2, Users, TrendingUp } from "lucide-react"
import { Badge } from "@/components/ui/badge"
import { LoyaltyData, LoyaltyReward, PointHistoryItem, LoyaltyTier } from "@/lib/types/loyalty"
import { format, parseISO } from "date-fns"
import { CurrencyDisplay } from "@/components/ui/currency-display"
import { useCurrency } from "@/lib/currency-provider"
import { RewardManagementDialog } from "@/components/loyalty/reward-management-dialog"
import { PointAdjustmentDialog } from "@/components/loyalty/point-adjustment-dialog"
import { TierManagementDialog } from "@/components/loyalty/tier-management-dialog"
import { rewardTemplates, loyaltyTiers } from "@/lib/loyalty-management-data"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog"

export default function AdminLoyaltyPage() {
  const router = useRouter()
  const { toast } = useToast()
  const { currency, formatCurrency } = useCurrency()
  const [loading, setLoading] = useState(true)
  const [activeTab, setActiveTab] = useState("rewards")

  // Management dialog states
  const [isRewardManagementOpen, setIsRewardManagementOpen] = useState(false)
  const [isPointAdjustmentOpen, setIsPointAdjustmentOpen] = useState(false)
  const [isTierManagementOpen, setIsTierManagementOpen] = useState(false)
  const [editingReward, setEditingReward] = useState<LoyaltyReward | null>(null)
  const [editingTier, setEditingTier] = useState<LoyaltyTier | null>(null)

  // Data states
  const [rewards, setRewards] = useState<LoyaltyReward[]>([])
  const [tiers, setTiers] = useState<LoyaltyTier[]>([])
  const [selectedClient, setSelectedClient] = useState<string>("client123")

  useEffect(() => {
    // Check admin authentication here
    // For now, we'll just load the data
    fetchLoyaltyData()
  }, [])

  const fetchLoyaltyData = async () => {
    try {
      const response = await fetch(`/api/client-portal/loyalty?clientId=${selectedClient}`)
      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.error || "Failed to fetch loyalty data")
      }

      setRewards(data.loyalty.availableRewards)
      setTiers(loyaltyTiers)
    } catch (error) {
      console.error("Error fetching loyalty data:", error)
      toast({
        variant: "destructive",
        title: "Error",
        description: "Failed to fetch loyalty data. Please try again later.",
      })
    } finally {
      setLoading(false)
    }
  }

  // Reward management functions
  const handleSaveReward = async (reward: LoyaltyReward) => {
    try {
      const action = editingReward ? "update" : "create"

      const response = await fetch('/api/client-portal/loyalty', {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action,
          reward
        }),
      })

      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.error || `Failed to ${action} reward`)
      }

      // Update local state
      if (editingReward) {
        setRewards(prev => prev.map(r => r.id === reward.id ? reward : r))
        toast({
          title: "Reward updated",
          description: `${reward.name} has been updated successfully.`,
        })
      } else {
        setRewards(prev => [...prev, reward])
        toast({
          title: "Reward created",
          description: `${reward.name} has been created successfully.`,
        })
      }

      setEditingReward(null)
    } catch (error) {
      console.error("Error saving reward:", error)
      toast({
        variant: "destructive",
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to save reward. Please try again.",
      })
    }
  }

  const handleDeleteReward = async (reward: LoyaltyReward) => {
    try {
      const response = await fetch('/api/client-portal/loyalty', {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: "delete",
          reward
        }),
      })

      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.error || "Failed to delete reward")
      }

      setRewards(prev => prev.filter(r => r.id !== reward.id))
      toast({
        title: "Reward deleted",
        description: `${reward.name} has been deleted successfully.`,
      })
    } catch (error) {
      console.error("Error deleting reward:", error)
      toast({
        variant: "destructive",
        title: "Error",
        description: "Failed to delete reward. Please try again.",
      })
    }
  }

  const handleSaveTier = (tier: LoyaltyTier) => {
    if (editingTier) {
      setTiers(prev => prev.map(t => t.id === tier.id ? tier : t))
      toast({
        title: "Tier updated",
        description: `${tier.name} tier has been updated successfully.`,
      })
    } else {
      setTiers(prev => [...prev, tier])
      toast({
        title: "Tier created",
        description: `${tier.name} tier has been created successfully.`,
      })
    }
    setEditingTier(null)
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-96">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-pink-600"></div>
      </div>
    )
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-8 gap-4">
        <div>
          <h1 className="text-2xl font-bold mb-1">Loyalty Program Management</h1>
          <p className="text-gray-600">Manage rewards, tiers, and customer loyalty programs</p>
        </div>
      </div>

      <Tabs defaultValue="rewards" value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList className="grid grid-cols-4 w-full max-w-2xl">
          <TabsTrigger value="rewards">Rewards</TabsTrigger>
          <TabsTrigger value="tiers">Tiers</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
          <TabsTrigger value="clients">Clients</TabsTrigger>
        </TabsList>

        {/* Rewards Management Tab */}
        <TabsContent value="rewards" className="space-y-6">
          <div className="flex justify-between items-center">
            <div>
              <h3 className="text-lg font-medium">Reward Management</h3>
              <p className="text-sm text-gray-600">Create and manage loyalty rewards</p>
            </div>
            <Button
              onClick={() => {
                setEditingReward(null)
                setIsRewardManagementOpen(true)
              }}
              className="bg-pink-600 hover:bg-pink-700"
            >
              <Plus className="mr-2 h-4 w-4" />
              Create Reward
            </Button>
          </div>

          <Card>
            <CardHeader>
              <CardTitle>Available Rewards</CardTitle>
              <CardDescription>Manage your loyalty program rewards</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {rewards.map((reward) => (
                  <Card key={reward.id} className="overflow-hidden">
                    <CardContent className="p-4">
                      <div className="flex justify-between items-start">
                        <div className="space-y-2 flex-1">
                          <div className="flex items-center gap-2">
                            <h4 className="font-medium">{reward.name}</h4>
                            <Badge variant={reward.isActive ? "default" : "secondary"}>
                              {reward.isActive ? "Active" : "Inactive"}
                            </Badge>
                            <Badge variant="outline">{reward.category}</Badge>
                          </div>
                          <p className="text-sm text-gray-600">{reward.description}</p>
                          <div className="flex items-center gap-4 text-xs text-gray-500">
                            <span>Cost: {reward.pointsCost} points</span>
                            {reward.value && <span>Value: <CurrencyDisplay amount={reward.value} /></span>}
                            <span>Used: {reward.usageCount}/{reward.usageLimit || "∞"}</span>
                            <span>Expires: {format(parseISO(reward.expiresAt), "MMM dd, yyyy")}</span>
                          </div>
                        </div>
                        <div className="flex gap-2">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => {
                              setEditingReward(reward)
                              setIsRewardManagementOpen(true)
                            }}
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                          <AlertDialog>
                            <AlertDialogTrigger asChild>
                              <Button variant="outline" size="sm" className="text-red-600 hover:text-red-700">
                                <Trash2 className="h-4 w-4" />
                              </Button>
                            </AlertDialogTrigger>
                            <AlertDialogContent>
                              <AlertDialogHeader>
                                <AlertDialogTitle>Delete Reward</AlertDialogTitle>
                                <AlertDialogDescription>
                                  Are you sure you want to delete "{reward.name}"? This action cannot be undone.
                                </AlertDialogDescription>
                              </AlertDialogHeader>
                              <AlertDialogFooter>
                                <AlertDialogCancel>Cancel</AlertDialogCancel>
                                <AlertDialogAction
                                  onClick={() => handleDeleteReward(reward)}
                                  className="bg-red-600 hover:bg-red-700"
                                >
                                  Delete
                                </AlertDialogAction>
                              </AlertDialogFooter>
                            </AlertDialogContent>
                          </AlertDialog>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Tiers Management Tab */}
        <TabsContent value="tiers" className="space-y-6">
          <div className="flex justify-between items-center">
            <div>
              <h3 className="text-lg font-medium">Tier Management</h3>
              <p className="text-sm text-gray-600">Configure loyalty tiers and benefits</p>
            </div>
            <Button
              onClick={() => {
                setEditingTier(null)
                setIsTierManagementOpen(true)
              }}
              className="bg-pink-600 hover:bg-pink-700"
            >
              <Plus className="mr-2 h-4 w-4" />
              Create Tier
            </Button>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {tiers.map((tier) => (
              <Card key={tier.id} className="overflow-hidden">
                <CardContent className="p-6">
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <Badge className={tier.color}>
                        {tier.name}
                      </Badge>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => {
                          setEditingTier(tier)
                          setIsTierManagementOpen(true)
                        }}
                      >
                        <Edit className="h-4 w-4" />
                      </Button>
                    </div>
                    <p className="text-sm text-gray-600">{tier.description}</p>
                    <div className="space-y-2">
                      <div className="flex justify-between text-xs">
                        <span>Points Range:</span>
                        <span>{tier.minPoints}+ points</span>
                      </div>
                      <div className="flex justify-between text-xs">
                        <span>Multiplier:</span>
                        <span>{tier.pointsMultiplier}x</span>
                      </div>
                      <div className="flex justify-between text-xs">
                        <span>Benefits:</span>
                        <span>{tier.benefits.length} items</span>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        {/* Analytics Tab */}
        <TabsContent value="analytics" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-gray-600">Total Rewards</p>
                    <p className="text-3xl font-bold text-pink-600">{rewards.length}</p>
                  </div>
                  <Gift className="h-8 w-8 text-pink-600" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-gray-600">Active Rewards</p>
                    <p className="text-3xl font-bold text-green-600">{rewards.filter(r => r.isActive).length}</p>
                  </div>
                  <Star className="h-8 w-8 text-green-600" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-gray-600">Loyalty Tiers</p>
                    <p className="text-3xl font-bold text-purple-600">{tiers.length}</p>
                  </div>
                  <Award className="h-8 w-8 text-purple-600" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-gray-600">Total Redemptions</p>
                    <p className="text-3xl font-bold text-blue-600">{rewards.reduce((sum, r) => sum + r.usageCount, 0)}</p>
                  </div>
                  <TrendingUp className="h-8 w-8 text-blue-600" />
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Client Management Tab */}
        <TabsContent value="clients" className="space-y-6">
          <div className="flex justify-between items-center">
            <div>
              <h3 className="text-lg font-medium">Client Point Management</h3>
              <p className="text-sm text-gray-600">Manage individual client points and adjustments</p>
            </div>
            <Button
              onClick={() => setIsPointAdjustmentOpen(true)}
              className="bg-pink-600 hover:bg-pink-700"
            >
              <Star className="mr-2 h-4 w-4" />
              Adjust Points
            </Button>
          </div>

          <Card>
            <CardHeader>
              <CardTitle>Client Selection</CardTitle>
              <CardDescription>Select a client to manage their loyalty points</CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-gray-500">Client management interface would go here</p>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Management Dialogs */}
      <RewardManagementDialog
        open={isRewardManagementOpen}
        onOpenChange={setIsRewardManagementOpen}
        reward={editingReward}
        onSave={handleSaveReward}
      />

      <PointAdjustmentDialog
        open={isPointAdjustmentOpen}
        onOpenChange={setIsPointAdjustmentOpen}
        clientId={selectedClient}
        clientName="Selected Client"
        currentPoints={450}
        onAdjustment={(adjustment) => {
          toast({
            title: "Points adjusted",
            description: "Client points have been updated successfully.",
          })
        }}
      />

      <TierManagementDialog
        open={isTierManagementOpen}
        onOpenChange={setIsTierManagementOpen}
        tier={editingTier}
        onSave={handleSaveTier}
        existingTiers={tiers}
      />
    </div>
  )
}
