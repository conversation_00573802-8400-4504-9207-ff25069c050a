let userConfig = undefined
try {
  // try to import ESM first
  userConfig = await import('./v0-user-next.config.mjs')
} catch (e) {
  try {
    // fallback to CJS import
    userConfig = await import("./v0-user-next.config");
  } catch (innerError) {
    // ignore error
  }
}

/** @type {import('next').NextConfig} */
const nextConfig = {
  eslint: {
    ignoreDuringBuilds: true,
  },
  typescript: {
    ignoreBuildErrors: true,
  },
  images: {
    remotePatterns: [
      {
        protocol: 'https',
        hostname: 'hebbkx1anhila5yf.public.blob.vercel-storage.com',
        pathname: '/**',
      },
      {
        protocol: 'https',
        hostname: 'images.unsplash.com',
        pathname: '/**',
      }
    ],
    formats: ['image/avif', 'image/webp'],
    minimumCacheTTL: 60, // Cache images for at least 60 seconds
  },
  experimental: {
    // Next.js 15 features that are supported in the stable version
    staleTimes: {
      dynamic: 0, // Dynamic routes are always fresh
      static: 180, // Static routes are cached for 3 minutes
    },
    // Note: dynamicIO requires Next.js canary version

    // Existing experimental features
    webpackBuildWorker: true,
    parallelServerBuildTraces: true,
    parallelServerCompiles: true,
    optimizeCss: true, // Optimize CSS for production builds

    // Enhanced package optimizations
    optimizePackageImports: [
      'recharts',
      '@radix-ui/react-icons',
      'lucide-react',
      '@radix-ui/react-dialog',
      '@radix-ui/react-dropdown-menu',
      '@radix-ui/react-popover',
      '@radix-ui/react-tabs',
      '@radix-ui/react-select',
      '@radix-ui/react-toast',
      'date-fns',
      'sonner',
      'react-day-picker',
      'react-hook-form',
      'zod',
      'class-variance-authority',
      'clsx',
      'tailwind-merge'
    ],
  },
  // Improve performance by enabling React optimizations
  reactStrictMode: true,
  // SWC minification is now enabled by default in Next.js 13+
  // Improve performance by enabling compression
  compress: true,
  // Improve performance by enabling HTTP/2 server push
  poweredByHeader: false,
  // Improve performance by enabling persistent caching
  onDemandEntries: {
    // Keep pages in memory for 5 minutes
    maxInactiveAge: 5 * 60 * 1000,
    // Cache up to 15 pages in memory
    pagesBufferLength: 15,
  },
}

if (userConfig) {
  // ESM imports will have a "default" property
  const config = userConfig.default || userConfig

  for (const key in config) {
    if (
      typeof nextConfig[key] === 'object' &&
      !Array.isArray(nextConfig[key])
    ) {
      nextConfig[key] = {
        ...nextConfig[key],
        ...config[key],
      }
    } else {
      nextConfig[key] = config[key]
    }
  }
}

export default nextConfig
