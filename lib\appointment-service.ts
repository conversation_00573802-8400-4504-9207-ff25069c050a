// Centralized appointment management service
// This service handles all appointment operations and ensures data consistency
// between the client portal and the appointment calendar

import { mockAppointments } from '@/lib/mock-data';
import { appointments } from '@/lib/appointments-data';
import { AppointmentStatus } from '@/lib/types/appointment';

// Define the appointment interface
export interface AppointmentData {
  id: string;
  clientId: string;
  clientName: string;
  staffId: string;
  staffName: string;
  service: string;
  serviceId?: string;
  date: string; // ISO date string
  duration: number; // in minutes
  location: string;
  price?: number;
  notes?: string;
  status: AppointmentStatus | string;
  statusHistory?: Array<{
    status: string;
    timestamp: string;
    updatedBy?: string;
  }>;
  type?: string;
  additionalServices?: Array<any>;
  products?: Array<any>;
  createdAt?: string;
  updatedAt?: string;
  [key: string]: any; // Allow additional properties
}

// Storage key for localStorage
const STORAGE_KEY = 'vanity_appointments';

// Debug flag - set to true to enable console logging
const DEBUG = true;

/**
 * Get all appointments from all sources
 * This combines appointments from localStorage, mockAppointments, and appointments arrays
 */
export function getAllAppointments(): AppointmentData[] {
  // Try to get appointments from localStorage first
  let localStorageAppointments: AppointmentData[] = [];
  try {
    const storedData = typeof window !== 'undefined' ? localStorage.getItem(STORAGE_KEY) : null;
    if (storedData) {
      localStorageAppointments = JSON.parse(storedData);
      if (DEBUG) console.log('AppointmentService: Loaded from localStorage', localStorageAppointments.length);
    }
  } catch (error) {
    console.error('AppointmentService: Error loading from localStorage', error);
  }

  // Combine with mockAppointments and appointments arrays
  // Use a Map to deduplicate by ID
  const appointmentMap = new Map<string, AppointmentData>();
  
  // Add localStorage appointments first (they have priority)
  localStorageAppointments.forEach(appointment => {
    appointmentMap.set(appointment.id, appointment);
  });
  
  // Add mockAppointments next
  mockAppointments.forEach(appointment => {
    if (!appointmentMap.has(appointment.id)) {
      appointmentMap.set(appointment.id, appointment as AppointmentData);
    }
  });
  
  // Add appointments from appointments-data.ts last
  appointments.forEach(appointment => {
    if (!appointmentMap.has(appointment.id)) {
      appointmentMap.set(appointment.id, appointment as AppointmentData);
    }
  });
  
  // Convert Map back to array
  const allAppointments = Array.from(appointmentMap.values());
  
  if (DEBUG) console.log('AppointmentService: Combined appointments', allAppointments.length);
  return allAppointments;
}

/**
 * Save appointments to all storage locations
 */
export function saveAppointments(appointments: AppointmentData[]): void {
  if (DEBUG) console.log('AppointmentService: Saving appointments', appointments.length);
  
  // Save to localStorage
  try {
    if (typeof window !== 'undefined') {
      localStorage.setItem(STORAGE_KEY, JSON.stringify(appointments));
      if (DEBUG) console.log('AppointmentService: Saved to localStorage');
    }
  } catch (error) {
    console.error('AppointmentService: Error saving to localStorage', error);
  }
  
  // Update mockAppointments array (in-memory only)
  try {
    // Clear the array
    mockAppointments.length = 0;
    // Add all appointments
    appointments.forEach(appointment => {
      mockAppointments.push(appointment as any);
    });
    if (DEBUG) console.log('AppointmentService: Updated mockAppointments array');
  } catch (error) {
    console.error('AppointmentService: Error updating mockAppointments', error);
  }
}

/**
 * Add a new appointment
 */
export function addAppointment(appointment: AppointmentData): AppointmentData {
  if (DEBUG) console.log('AppointmentService: Adding appointment', appointment);
  
  // Get all existing appointments
  const allAppointments = getAllAppointments();
  
  // Check if appointment with this ID already exists
  const existingIndex = allAppointments.findIndex(a => a.id === appointment.id);
  
  if (existingIndex >= 0) {
    // Update existing appointment
    allAppointments[existingIndex] = appointment;
    if (DEBUG) console.log('AppointmentService: Updated existing appointment');
  } else {
    // Add new appointment
    allAppointments.push(appointment);
    if (DEBUG) console.log('AppointmentService: Added new appointment');
  }
  
  // Save appointments to all storage locations
  saveAppointments(allAppointments);
  
  return appointment;
}

/**
 * Update an existing appointment
 */
export function updateAppointment(appointmentId: string, updates: Partial<AppointmentData>): AppointmentData | null {
  if (DEBUG) console.log('AppointmentService: Updating appointment', appointmentId, updates);
  
  // Get all existing appointments
  const allAppointments = getAllAppointments();
  
  // Find the appointment to update
  const appointmentIndex = allAppointments.findIndex(a => a.id === appointmentId);
  
  if (appointmentIndex >= 0) {
    // Update the appointment
    const updatedAppointment = {
      ...allAppointments[appointmentIndex],
      ...updates,
      updatedAt: new Date().toISOString()
    };
    
    allAppointments[appointmentIndex] = updatedAppointment;
    
    // Save appointments to all storage locations
    saveAppointments(allAppointments);
    
    if (DEBUG) console.log('AppointmentService: Appointment updated successfully');
    return updatedAppointment;
  }
  
  if (DEBUG) console.log('AppointmentService: Appointment not found for update');
  return null;
}

/**
 * Delete an appointment
 */
export function deleteAppointment(appointmentId: string): boolean {
  if (DEBUG) console.log('AppointmentService: Deleting appointment', appointmentId);
  
  // Get all existing appointments
  const allAppointments = getAllAppointments();
  
  // Filter out the appointment to delete
  const filteredAppointments = allAppointments.filter(a => a.id !== appointmentId);
  
  if (filteredAppointments.length < allAppointments.length) {
    // Save appointments to all storage locations
    saveAppointments(filteredAppointments);
    
    if (DEBUG) console.log('AppointmentService: Appointment deleted successfully');
    return true;
  }
  
  if (DEBUG) console.log('AppointmentService: Appointment not found for deletion');
  return false;
}

/**
 * Get appointment by ID
 */
export function getAppointmentById(appointmentId: string): AppointmentData | null {
  // Get all existing appointments
  const allAppointments = getAllAppointments();
  
  // Find the appointment
  const appointment = allAppointments.find(a => a.id === appointmentId);
  
  return appointment || null;
}

/**
 * Initialize the appointment service
 * This ensures that all storage locations are in sync
 */
export function initializeAppointmentService(): void {
  if (DEBUG) console.log('AppointmentService: Initializing');
  
  // Get all appointments and save them back to ensure consistency
  const allAppointments = getAllAppointments();
  saveAppointments(allAppointments);
  
  if (DEBUG) console.log('AppointmentService: Initialized with', allAppointments.length, 'appointments');
}
