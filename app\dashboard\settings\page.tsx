"use client"
import { useAuth } from "@/lib/auth-provider"
import { Card, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, <PERSON><PERSON><PERSON>rig<PERSON> } from "@/components/ui/tabs"
import { GeneralSettings } from "@/components/settings/general-settings"
import { LocationSettings } from "@/components/settings/location-settings"
import { UserSettings } from "@/components/settings/user-settings"
import { IntegrationSettings } from "@/components/settings/integration-settings"
import { NotificationSettings } from "@/components/settings/notification-settings"
import { CurrencyDetectorTest } from "@/components/settings/currency-detector-test"
import { AccessDenied } from "@/components/access-denied"

export default function SettingsPage() {
  const { hasPermission } = useAuth()

  // Check if user has permission to view settings page
  if (!hasPermission("view_settings")) {
    return (
      <AccessDenied
        description="You don't have permission to view the settings page."
        backButtonHref="/dashboard"
      />
    )
  }

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-2xl font-bold tracking-tight">Settings</h2>
        <p className="text-muted-foreground">Manage your salon settings and preferences</p>
      </div>

      <Tabs defaultValue="general" className="w-full">
        <TabsList className="mb-4">
          <TabsTrigger value="general">General</TabsTrigger>
          <TabsTrigger value="locations">Locations</TabsTrigger>
          <TabsTrigger value="users">Users & Permissions</TabsTrigger>
          <TabsTrigger value="integrations">Integrations</TabsTrigger>
          <TabsTrigger value="notifications">Notifications</TabsTrigger>
        </TabsList>

        <TabsContent value="general">
          <div className="space-y-6">
            <GeneralSettings />
            {process.env.NODE_ENV === 'development' && <CurrencyDetectorTest />}
          </div>
        </TabsContent>

        <TabsContent value="locations">
          <LocationSettings />
        </TabsContent>

        <TabsContent value="users">
          <UserSettings />
        </TabsContent>

        <TabsContent value="integrations">
          <IntegrationSettings />
        </TabsContent>

        <TabsContent value="notifications">
          <NotificationSettings />
        </TabsContent>
      </Tabs>
    </div>
  )
}

