// Mock clients data
export const mockClients = [
  {
    id: "ed1",
    name: "<PERSON>",
    email: "<EMAIL>",
    phone: "(*************",
    lastVisit: "Apr 1, 2025",
    preferredLocation: "loc1",
    locations: ["loc1", "loc2"],
    status: "Active",
    avatar: "ED",
    segment: "VIP",
    totalSpent: 1250.75,
  },
  {
    id: "jw2",
    name: "<PERSON>",
    email: "<EMAIL>",
    phone: "(*************",
    lastVisit: "Mar 25, 2025",
    preferredLocation: "loc1",
    locations: ["loc1"],
    status: "Active",
    avatar: "JW",
    segment: "Regular",
    totalSpent: 875.5,
  },
  {
    id: "om3",
    name: "<PERSON>",
    email: "<EMAIL>",
    phone: "(*************",
    lastVisit: "Mar 20, 2025",
    preferredLocation: "loc2",
    locations: ["loc1", "loc2"],
    status: "Active",
    avatar: "OM",
    segment: "Regular",
    totalSpent: 650.25,
  },
  {
    id: "wb4",
    name: "<PERSON>",
    email: "will<PERSON>.<EMAIL>",
    phone: "(*************",
    lastVisit: "Mar 15, 2025",
    preferredLocation: "loc3",
    locations: ["loc3"],
    status: "Active",
    avatar: "WB",
    segment: "New",
    totalSpent: 225.0,
  },
  {
    id: "st5",
    name: "Sophia Thompson",
    email: "<EMAIL>",
    phone: "(*************",
    lastVisit: "Feb 28, 2025",
    preferredLocation: "loc1",
    locations: ["loc1"],
    status: "Inactive",
    avatar: "ST",
    segment: "At Risk",
    totalSpent: 1875.25,
  },
  {
    id: "sj6",
    name: "Sarah Johnson",
    email: "<EMAIL>",
    phone: "(*************",
    lastVisit: "Apr 1, 2025",
    preferredLocation: "loc3",
    locations: ["loc3"],
    status: "Active",
    avatar: "SJ",
    segment: "Regular",
    totalSpent: 450.0,
  },
  {
    id: "mt7",
    name: "Michael Thompson",
    email: "<EMAIL>",
    phone: "(*************",
    lastVisit: "Mar 28, 2025",
    preferredLocation: "loc2",
    locations: ["loc2"],
    status: "Active",
    avatar: "MT",
    segment: "VIP",
    totalSpent: 1350.0,
  },
]

// Mock staff data
export const mockStaff = [
  {
    id: "1",
    name: "Emma Johnson",
    role: "stylist",
    email: "<EMAIL>",
    phone: "(*************",
    locations: ["loc1", "loc2"],
    status: "Active",
    avatar: "EJ",
    color: "bg-purple-100 text-purple-800",
    homeService: true,
  },
  {
    id: "2",
    name: "Michael Chen",
    role: "colorist",
    email: "<EMAIL>",
    phone: "(*************",
    locations: ["loc1"],
    status: "Active",
    avatar: "MC",
    color: "bg-blue-100 text-blue-800",
    homeService: true,
  },
  {
    id: "3",
    name: "Sophia Rodriguez",
    role: "nail_technician",
    email: "<EMAIL>",
    phone: "(*************",
    locations: ["loc1", "loc3"],
    status: "Active",
    avatar: "SR",
    color: "bg-pink-100 text-pink-800",
    homeService: true,
  },
  {
    id: "4",
    name: "David Kim",
    role: "stylist",
    email: "<EMAIL>",
    phone: "(*************",
    locations: ["loc1"],
    status: "Active",
    avatar: "DK",
    color: "bg-orange-100 text-orange-800",
    homeService: true,
  },
  {
    id: "5",
    name: "Jessica Lee",
    role: "esthetician",
    email: "<EMAIL>",
    phone: "(*************",
    locations: ["loc3"],
    status: "Active",
    avatar: "JL",
    color: "bg-green-100 text-green-800",
    homeService: true,
  },
  {
    id: "6",
    name: "Robert Taylor",
    role: "barber",
    email: "<EMAIL>",
    phone: "(*************",
    locations: ["loc1"],
    status: "Active",
    avatar: "RT",
    color: "bg-rose-100 text-rose-800",
  },
  // New staff members for D-Ring Road (loc1)
  {
    id: "7",
    name: "Olivia Wilson",
    role: "stylist",
    email: "<EMAIL>",
    phone: "(*************",
    locations: ["loc1"],
    status: "Active",
    avatar: "OW",
    color: "bg-indigo-100 text-indigo-800",
    homeService: true,
  },
  {
    id: "8",
    name: "James Martinez",
    role: "barber",
    email: "<EMAIL>",
    phone: "(*************",
    locations: ["loc1"],
    status: "Active",
    avatar: "JM",
    color: "bg-amber-100 text-amber-800",
  },
  // New staff members for Muaither (loc2)
  {
    id: "9",
    name: "Ava Thompson",
    role: "colorist",
    email: "<EMAIL>",
    phone: "(*************",
    locations: ["loc2"],
    status: "Active",
    avatar: "AT",
    color: "bg-cyan-100 text-cyan-800",
    homeService: true,
  },
  {
    id: "10",
    name: "Noah Garcia",
    role: "stylist",
    email: "<EMAIL>",
    phone: "(*************",
    locations: ["loc2"],
    status: "Active",
    avatar: "NG",
    color: "bg-lime-100 text-lime-800",
  },
  // New staff members for Medinat Khalifa (loc3)
  {
    id: "11",
    name: "Isabella Brown",
    role: "nail_technician",
    email: "<EMAIL>",
    phone: "(*************",
    locations: ["loc3"],
    status: "Active",
    avatar: "IB",
    color: "bg-fuchsia-100 text-fuchsia-800",
    homeService: true,
  },
  {
    id: "12",
    name: "Liam Davis",
    role: "esthetician",
    email: "<EMAIL>",
    phone: "(*************",
    locations: ["loc3"],
    status: "Active",
    avatar: "LD",
    color: "bg-emerald-100 text-emerald-800",
  },
  // Staff dedicated for Home service only
  {
    id: "13",
    name: "Ethan Wright",
    role: "stylist",
    email: "<EMAIL>",
    phone: "(*************",
    locations: ["home"],
    status: "Active",
    avatar: "EW",
    color: "bg-violet-100 text-violet-800",
    homeService: true,
  },
  {
    id: "14",
    name: "Mia Parker",
    role: "nail_technician",
    email: "<EMAIL>",
    phone: "(*************",
    locations: ["home"],
    status: "Active",
    avatar: "MP",
    color: "bg-yellow-100 text-yellow-800",
    homeService: true,
  },
]

// Mock services data
export const mockServices = [
  // Hair Services
  { id: "1", name: "Haircut & Style", category: "Hair", price: 75, duration: 60, locations: ["loc1", "loc2", "loc3"] },
  { id: "2", name: "Men's Haircut", category: "Hair", price: 55, duration: 45, locations: ["loc1", "loc2", "loc3"] },
  { id: "3", name: "Beard Trim", category: "Hair", price: 35, duration: 30, locations: ["loc1", "loc3"] },
  { id: "4", name: "Blowout", category: "Hair", price: 65, duration: 45, locations: ["loc1", "loc2", "loc3"] },
  { id: "5", name: "Deep Conditioning", category: "Hair", price: 45, duration: 30, locations: ["loc1", "loc2", "loc3"] },

  // Color Services
  { id: "6", name: "Color & Highlights", category: "Color", price: 150, duration: 120, locations: ["loc1", "loc2", "loc3"] },
  { id: "7", name: "Balayage", category: "Color", price: 180, duration: 150, locations: ["loc1", "loc2"] },

  // Nails Services
  { id: "8", name: "Manicure & Pedicure", category: "Nails", price: 95, duration: 90, locations: ["loc2"] },
  { id: "9", name: "Gel Nails", category: "Nails", price: 65, duration: 60, locations: ["loc1", "loc2"] },

  // Skin Services
  { id: "10", name: "Facial Treatment", category: "Skin", price: 85, duration: 60, locations: ["loc2", "loc3"] },
  { id: "11", name: "Eyebrow Waxing", category: "Skin", price: 25, duration: 15, locations: ["loc2", "loc3"] },

  // Massage Services
  { id: "12", name: "Massage Therapy", category: "Massage", price: 90, duration: 60, locations: ["loc3"] },
  { id: "13", name: "Hot Stone Massage", category: "Massage", price: 120, duration: 90, locations: ["loc3"] },

  // Henna Services
  { id: "14", name: "Bridal Henna", category: "Henna", price: 150, duration: 120, locations: ["loc1", "home"] },
  { id: "15", name: "Hand Henna", category: "Henna", price: 65, duration: 45, locations: ["loc1", "loc2", "home"] },
  { id: "16", name: "Feet Henna", category: "Henna", price: 65, duration: 45, locations: ["loc1", "loc2", "home"] },

  // Weyba Tis Services (Ethiopian Traditional Spa)
  { id: "17", name: "Weyba Tis Full Body", category: "Weyba Tis", price: 180, duration: 120, locations: ["loc3", "home"] },
  { id: "18", name: "Weyba Tis Face Treatment", category: "Weyba Tis", price: 85, duration: 60, locations: ["loc3", "home"] },
  { id: "19", name: "Weyba Tis Back Treatment", category: "Weyba Tis", price: 95, duration: 60, locations: ["loc3", "home"] },
  { id: "20", name: "Weyba Tis Herbal Steam", category: "Weyba Tis", price: 75, duration: 45, locations: ["loc3", "home"] },

  // Makeup Services
  { id: "21", name: "Bridal Makeup", category: "Makeup", price: 150, duration: 90, locations: ["loc1", "loc2"] },
  { id: "22", name: "Special Occasion Makeup", category: "Makeup", price: 95, duration: 60, locations: ["loc1", "loc2", "loc3"] },
  { id: "23", name: "Makeup Lesson", category: "Makeup", price: 120, duration: 90, locations: ["loc1", "loc2"] },

  // Waxing Services
  { id: "24", name: "Full Body Waxing", category: "Waxing", price: 180, duration: 120, locations: ["loc2", "loc3"] },
  { id: "25", name: "Leg Waxing", category: "Waxing", price: 65, duration: 45, locations: ["loc2", "loc3"] },
  { id: "26", name: "Brazilian Waxing", category: "Waxing", price: 85, duration: 60, locations: ["loc2", "loc3"] },

  // Extensions Services
  { id: "27", name: "Hair Extensions Application", category: "Extensions", price: 250, duration: 180, locations: ["loc1", "loc2"] },
  { id: "28", name: "Extensions Maintenance", category: "Extensions", price: 120, duration: 90, locations: ["loc1", "loc2"] },

  // Bridal Services
  { id: "29", name: "Bridal Package", category: "Bridal", price: 350, duration: 240, locations: ["loc1", "loc2"] },
  { id: "30", name: "Bridal Trial", category: "Bridal", price: 150, duration: 120, locations: ["loc1", "loc2"] }
]

// Mock service categories
export const mockCategories = [
  { id: "1", name: "Hair", description: "Haircuts, styling, and treatments", serviceCount: 5 },
  { id: "2", name: "Color", description: "Hair coloring services", serviceCount: 2 },
  { id: "3", name: "Nails", description: "Manicure and pedicure services", serviceCount: 2 },
  { id: "4", name: "Skin", description: "Facial treatments and skincare", serviceCount: 2 },
  { id: "5", name: "Massage", description: "Massage therapy services", serviceCount: 2 },
  { id: "6", name: "Henna", description: "Traditional henna art and treatments", serviceCount: 3 },
  { id: "7", name: "Weyba Tis", description: "Ethiopian traditional spa treatments", serviceCount: 4 },
  { id: "8", name: "Makeup", description: "Professional makeup services", serviceCount: 3 },
  { id: "9", name: "Waxing", description: "Hair removal services", serviceCount: 3 },
  { id: "10", name: "Extensions", description: "Hair extension services", serviceCount: 2 },
  { id: "11", name: "Bridal", description: "Bridal packages and services", serviceCount: 2 },
]

// Mock transactions data
export const mockTransactions = [
  {
    id: "TX-001",
    date: "Apr 1, 2025",
    client: "Jennifer Smith",
    type: "Service",
    description: "Haircut & Style, Deep Conditioning",
    amount: 120.0,
    paymentMethod: "Credit Card",
    status: "Completed",
  },
  {
    id: "TX-002",
    date: "Apr 1, 2025",
    client: "Michael Johnson",
    type: "Service",
    description: "Men's Haircut",
    amount: 55.0,
    paymentMethod: "Cash",
    status: "Completed",
  },
  {
    id: "TX-003",
    date: "Mar 31, 2025",
    client: "Sarah Williams",
    type: "Product",
    description: "Shampoo & Conditioner Set",
    amount: 45.0,
    paymentMethod: "Credit Card",
    status: "Completed",
  },
  {
    id: "TX-004",
    date: "Mar 31, 2025",
    client: "David Brown",
    type: "Service",
    description: "Color & Highlights",
    amount: 150.0,
    paymentMethod: "Credit Card",
    status: "Completed",
  },
  {
    id: "TX-005",
    date: "Mar 30, 2025",
    client: "Emily Davis",
    type: "Service + Product",
    description: "Blowout, Styling Products",
    amount: 95.0,
    paymentMethod: "Credit Card",
    status: "Completed",
  },
]

// Mock expenses data
export const mockExpenses = [
  {
    id: "EXP-001",
    date: "Apr 1, 2025",
    category: "Inventory",
    vendor: "Beauty Supply Co.",
    description: "Hair products restock",
    amount: 850.0,
    paymentMethod: "Credit Card",
    status: "Paid",
  },
  {
    id: "EXP-002",
    date: "Apr 1, 2025",
    category: "Utilities",
    vendor: "City Power & Water",
    description: "Monthly utilities",
    amount: 320.0,
    paymentMethod: "Bank Transfer",
    status: "Paid",
  },
  {
    id: "EXP-003",
    date: "Mar 31, 2025",
    category: "Rent",
    vendor: "Downtown Properties",
    description: "Monthly rent - D-Ring Road location",
    amount: 2200.0,
    paymentMethod: "Bank Transfer",
    status: "Paid",
  },
  {
    id: "EXP-004",
    date: "Mar 30, 2025",
    category: "Payroll",
    vendor: "Staff Payroll",
    description: "Bi-weekly staff payroll",
    amount: 4800.0,
    paymentMethod: "Bank Transfer",
    status: "Paid",
  },
  {
    id: "EXP-005",
    date: "Mar 28, 2025",
    category: "Marketing",
    vendor: "Social Media Ads",
    description: "Monthly social media advertising",
    amount: 250.0,
    paymentMethod: "Credit Card",
    status: "Paid",
  },
]

// Mock appointments data
export const mockAppointments = [
  {
    id: "a1",
    clientId: "ed1",
    clientName: "Emily Davis",
    staffId: "1",
    staffName: "Emma Johnson",
    service: "Haircut & Style",
    date: "2025-04-02T10:00:00",
    duration: 60,
    status: "confirmed",
    location: "loc1",
    price: 75,
    statusHistory: [
      {
        status: "pending",
        timestamp: "2025-04-01T13:05:00",
        updatedBy: "Client"
      },
      {
        status: "confirmed",
        timestamp: "2025-04-01T18:17:00",
        updatedBy: "Staff"
      }
    ]
  },
  {
    id: "a2",
    clientId: "jw2",
    clientName: "James Wilson",
    staffId: "2",
    staffName: "Michael Chen",
    service: "Color & Highlights",
    date: "2025-04-02T11:00:00",
    duration: 120,
    status: "confirmed",
    location: "loc1",
    price: 150,
    statusHistory: [
      {
        status: "pending",
        timestamp: "2025-04-01T09:30:00",
        updatedBy: "Client"
      },
      {
        status: "confirmed",
        timestamp: "2025-04-01T10:15:00",
        updatedBy: "Staff"
      }
    ]
  },
  {
    id: "a3",
    clientId: "om3",
    clientName: "Olivia Martinez",
    staffId: "3",
    staffName: "Sophia Rodriguez",
    service: "Manicure & Pedicure",
    date: "2025-04-02T13:00:00",
    duration: 90,
    status: "arrived",
    location: "loc2",
    price: 95,
    statusHistory: [
      {
        status: "pending",
        timestamp: "2025-04-01T14:20:00",
        updatedBy: "Client"
      },
      {
        status: "confirmed",
        timestamp: "2025-04-01T15:05:00",
        updatedBy: "Staff"
      },
      {
        status: "arrived",
        timestamp: "2025-04-02T12:44:00",
        updatedBy: "Staff"
      }
    ]
  },
  {
    id: "a4",
    clientId: "wb4",
    clientName: "William Brown",
    staffId: "4",
    staffName: "David Kim",
    service: "Men's Haircut",
    date: "2025-04-02T14:00:00",
    duration: 45,
    status: "completed",
    location: "loc2",
    price: 55,
    statusHistory: [
      {
        status: "pending",
        timestamp: "2025-04-01T11:30:00",
        updatedBy: "Client"
      },
      {
        status: "confirmed",
        timestamp: "2025-04-01T12:15:00",
        updatedBy: "Staff"
      },
      {
        status: "arrived",
        timestamp: "2025-04-02T13:50:00",
        updatedBy: "Staff"
      },
      {
        status: "service-started",
        timestamp: "2025-04-02T14:05:00",
        updatedBy: "Staff"
      },
      {
        status: "completed",
        timestamp: "2025-04-02T14:45:00",
        updatedBy: "Staff"
      }
    ]
  },
  {
    id: "a5",
    clientId: "st5",
    clientName: "Sophia Thompson",
    staffId: "5",
    staffName: "Jessica Lee",
    service: "Facial Treatment",
    date: "2025-04-02T15:00:00",
    duration: 60,
    status: "cancelled",
    location: "loc3",
    price: 85,
    statusHistory: [
      {
        status: "pending",
        timestamp: "2025-04-01T16:20:00",
        updatedBy: "Client"
      },
      {
        status: "confirmed",
        timestamp: "2025-04-01T17:05:00",
        updatedBy: "Staff"
      },
      {
        status: "cancelled",
        timestamp: "2025-04-02T09:30:00",
        updatedBy: "Client"
      }
    ]
  },
  {
    id: "a6",
    clientId: "sj6",
    clientName: "Sarah Johnson",
    staffId: "6",
    staffName: "Robert Taylor",
    service: "Beard Trim",
    date: "2025-04-26T16:30:00",
    duration: 30,
    status: "pending",
    location: "loc1",
    price: 25,
    statusHistory: [
      {
        status: "pending",
        timestamp: "2025-04-25T13:05:00",
        updatedBy: "Client"
      }
    ]
  },
  {
    id: "a7",
    clientId: "jw2",
    clientName: "James Wilson",
    staffId: "4",
    staffName: "David Kim",
    service: "Massage Therapy",
    date: "2025-04-02T18:45:00",
    duration: 60,
    status: "confirmed",
    location: "loc3",
    price: 90,
    statusHistory: [
      {
        status: "pending",
        timestamp: "2025-04-01T10:30:00",
        updatedBy: "Client"
      },
      {
        status: "confirmed",
        timestamp: "2025-04-01T11:45:00",
        updatedBy: "Staff"
      }
    ]
  },
  {
    id: "a8",
    clientId: "mt7",
    clientName: "Michael Thompson",
    staffId: "5",
    staffName: "Jessica Lee",
    service: "Color & Highlights",
    date: "2025-04-02T20:30:00",
    duration: 90,
    status: "confirmed",
    location: "loc2",
    price: 150,
    statusHistory: [
      {
        status: "pending",
        timestamp: "2025-04-01T15:10:00",
        updatedBy: "Client"
      },
      {
        status: "confirmed",
        timestamp: "2025-04-01T16:25:00",
        updatedBy: "Staff"
      }
    ]
  },
]

