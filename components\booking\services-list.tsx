"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Ta<PERSON>, <PERSON><PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { Scissors, Clock, Search } from "lucide-react"
import { Input } from "@/components/ui/input"

interface Service {
  id: string
  name: string
  description: string
  price: number
  duration: number
  category: string
}

interface ServicesListProps {
  location: string;
  categories: { id: string; name: string }[];
}

export function ServicesList({ location, categories }: ServicesListProps) {
  const [searchQuery, setSearchQuery] = useState("")
  const [selectedService, setSelectedService] = useState<string | null>(null)

  // Mock services data
  const services: Service[] = [
    {
      id: "s1",
      name: "Women's Haircut & Style",
      description: "Includes consultation, shampoo, cut, and blow dry styling",
      price: 85,
      duration: 60,
      category: "haircuts"
    },
    {
      id: "s2",
      name: "Men's Haircut",
      description: "Includes consultation, shampoo, cut, and style",
      price: 45,
      duration: 30,
      category: "haircuts"
    },
    {
      id: "s3",
      name: "Children's Haircut (Under 12)",
      description: "Basic haircut for children under 12 years old",
      price: 35,
      duration: 30,
      category: "haircuts"
    },
    {
      id: "s4",
      name: "Full Highlights",
      description: "Full head of highlights with foils, includes toner if needed",
      price: 150,
      duration: 120,
      category: "color"
    },
    {
      id: "s5",
      name: "Partial Highlights",
      description: "Partial highlights focused on top and face-framing sections",
      price: 100,
      duration: 90,
      category: "color"
    },
    {
      id: "s6",
      name: "Root Touch-Up",
      description: "Color application on roots only (up to 1 inch of regrowth)",
      price: 75,
      duration: 60,
      category: "color"
    },
    {
      id: "s7",
      name: "Balayage",
      description: "Hand-painted highlights for a natural, sun-kissed look",
      price: 175,
      duration: 150,
      category: "color"
    },
    {
      id: "s8",
      name: "Deep Conditioning Treatment",
      description: "Intensive moisture treatment to restore hair health",
      price: 35,
      duration: 30,
      category: "treatments"
    },
    {
      id: "s9",
      name: "Keratin Smoothing Treatment",
      description: "Reduces frizz and adds shine for up to 3 months",
      price: 250,
      duration: 180,
      category: "treatments"
    },
    {
      id: "s10",
      name: "Bridal Updo",
      description: "Formal updo styling for brides, includes consultation and trial",
      price: 120,
      duration: 90,
      category: "styling"
    },
    {
      id: "s11",
      name: "Blowout",
      description: "Shampoo and blow dry styling",
      price: 50,
      duration: 45,
      category: "styling"
    },
    {
      id: "s12",
      name: "Special Occasion Style",
      description: "Formal styling for special events",
      price: 85,
      duration: 60,
      category: "styling"
    }
  ]

  const filteredServices = searchQuery
    ? services.filter(service => 
        service.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        service.description.toLowerCase().includes(searchQuery.toLowerCase())
      )
    : services

  return (
    <Card>
      <CardHeader>
        <CardTitle>Select a Service</CardTitle>
        <CardDescription>Choose from our range of salon services</CardDescription>
        <div className="relative mt-4">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            type="search"
            placeholder="Search services..."
            className="pl-8"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>
      </CardHeader>
      <CardContent>
        {searchQuery ? (
          <div className="space-y-4">
            <h3 className="text-sm font-medium text-muted-foreground">Search Results</h3>
            {filteredServices.length === 0 ? (
              <p className="text-sm text-muted-foreground">No services found matching "{searchQuery}"</p>
            ) : (
              <div className="grid gap-4">
                {filteredServices.map((service) => (
                  <ServiceCard
                    key={service.id}
                    service={service}
                    isSelected={selectedService === service.id}
                    onSelect={() => setSelectedService(service.id)}
                  />
                ))}
              </div>
            )}
          </div>
        ) : (
          <Tabs defaultValue="haircuts" className="w-full">
            <TabsList className="mb-4 w-full grid grid-cols-4">
              {categories.map((category) => (
                <TabsTrigger key={category.id} value={category.id}>
                  {category.name}
                </TabsTrigger>
              ))}
            </TabsList>

            {categories.map((category) => (
              <TabsContent key={category.id} value={category.id} className="space-y-4">
                {services
                  .filter((service) => service.category === category.id)
                  .map((service) => (
                    <ServiceCard
                      key={service.id}
                      service={service}
                      isSelected={selectedService === service.id}
                      onSelect={() => setSelectedService(service.id)}
                    />
                  ))}
              </TabsContent>
            ))}
          </Tabs>
        )}
      </CardContent>
      <CardFooter className="flex justify-between">
        <Button variant="outline">Back</Button>
        <Button disabled={!selectedService}>Continue</Button>
      </CardFooter>
    </Card>
  )
}

interface ServiceCardProps {
  service: Service
  isSelected: boolean
  onSelect: () => void
}

function ServiceCard({ service, isSelected, onSelect }: ServiceCardProps) {
  return (
    <div
      className={`p-4 rounded-lg border cursor-pointer transition-colors ${
        isSelected
          ? "border-primary bg-primary/5"
          : "border-border hover:border-primary/50"
      }`}
      onClick={onSelect}
    >
      <div className="flex justify-between items-start">
        <div>
          <h3 className="font-medium">{service.name}</h3>
          <p className="text-sm text-muted-foreground mt-1">{service.description}</p>
          <div className="flex items-center gap-3 mt-2">
            <div className="flex items-center text-muted-foreground text-sm">
              <Clock className="h-3.5 w-3.5 mr-1" />
              {service.duration} min
            </div>
            <Badge variant="outline" className="text-xs">
              <Scissors className="h-3 w-3 mr-1" />
              {service.category === "haircuts"
                ? "Haircut"
                : service.category === "color"
                ? "Color"
                : service.category === "treatments"
                ? "Treatment"
                : "Styling"}
            </Badge>
          </div>
        </div>
        <div className="text-right">
          <span className="font-medium">${service.price}</span>
        </div>
      </div>
    </div>
  )
}
