"use client"

import { useState } from "react"
import { useAuth } from "@/lib/auth-provider"
import { useLocations } from "@/lib/location-provider"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from "@/components/ui/tabs"
import { ClientDirectory } from "@/components/clients/client-directory"
import { ClientSegments } from "@/components/clients/client-segments"
import { ClientCommunication } from "@/components/clients/client-communication"
import { EnhancedNewClientDialog } from "@/components/clients/enhanced-new-client-dialog"
import { Plus, Search } from "lucide-react"

export default function ClientsPage() {
  const { user, currentLocation } = useAuth()
  const { getLocationName } = useLocations()
  const [search, setSearch] = useState("")
  const [isDialogOpen, setIsDialogOpen] = useState(false)

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">Client Management</h2>
          <p className="text-muted-foreground">
            {currentLocation === "all"
              ? "Manage clients across all locations"
              : `Manage clients at ${getLocationName(currentLocation)} location`}
          </p>
        </div>
        <div className="flex items-center gap-2">
          <div className="relative">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search clients..."
              value={search}
              onChange={(e) => setSearch(e.target.value)}
              className="pl-8 w-[200px] md:w-[300px]"
            />
          </div>
          <Button onClick={() => setIsDialogOpen(true)}>
            <Plus className="mr-2 h-4 w-4" />
            Add Client
          </Button>
        </div>
      </div>

      <Tabs defaultValue="directory" className="w-full">
        <TabsList className="mb-4">
          <TabsTrigger value="directory">Directory</TabsTrigger>
          <TabsTrigger value="segments">Segments</TabsTrigger>
          <TabsTrigger value="communication">Communication</TabsTrigger>
        </TabsList>

        <TabsContent value="directory">
          <ClientDirectory search={search} />
        </TabsContent>

        <TabsContent value="segments">
          <ClientSegments />
        </TabsContent>

        <TabsContent value="communication">
          <ClientCommunication />
        </TabsContent>
      </Tabs>

      <EnhancedNewClientDialog open={isDialogOpen} onOpenChange={setIsDialogOpen} />
    </div>
  )
}

