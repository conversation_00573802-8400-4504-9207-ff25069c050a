import { NextResponse } from "next/server"
import { clientsRepository } from "@/lib/db"

export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url)
    const locationId = searchParams.get("locationId")

    let clients
    if (locationId) {
      clients = await clientsRepository.findByLocation(Number.parseInt(locationId))
    } else {
      clients = await clientsRepository.findAll()
    }

    return NextResponse.json({ clients })
  } catch (error) {
    console.error("Error fetching clients:", error)
    return NextResponse.json({ error: "Failed to fetch clients" }, { status: 500 })
  }
}

export async function POST(request: Request) {
  try {
    const data = await request.json()

    // Validate required fields
    if (!data.name || !data.phone) {
      return NextResponse.json({ error: "Name and phone are required" }, { status: 400 })
    }

    // Create the client
    const client = await clientsRepository.create({
      name: data.name,
      email: data.email || null,
      phone: data.phone,
      address: data.address || null,
      notes: data.notes || null,
      preferred_location_id: data.preferredLocationId ? Number.parseInt(data.preferredLocationId) : null,
    })

    // Add client to locations
    if (data.locations && Array.isArray(data.locations) && data.locations.length > 0) {
      const { query } = await import("@/lib/db")

      for (const locationId of data.locations) {
        await query(
          `INSERT INTO client_locations (client_id, location_id) 
           VALUES ($1, $2)`,
          [client.id, Number.parseInt(locationId)],
        )
      }
    }

    // Store client preferences if provided
    if (data.preferences) {
      const { query } = await import("@/lib/db")
      
      await query(
        `INSERT INTO client_preferences (client_id, preferences) 
         VALUES ($1, $2)`,
        [client.id, JSON.stringify(data.preferences)],
      )
    }

    return NextResponse.json({ client })
  } catch (error) {
    console.error("Error creating client:", error)
    return NextResponse.json({ error: "Failed to create client" }, { status: 500 })
  }
}
