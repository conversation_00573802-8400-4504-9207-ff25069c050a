import { NextRequest, NextResponse } from 'next/server';
import { StaffStorage } from '@/lib/staff-storage';
import { DATA_CACHE_TAGS } from '@/lib/next-cache';

/**
 * GET /api/staff
 * 
 * Fetch all staff members
 */
export async function GET(request: NextRequest) {
  try {
    // Get query parameters
    const searchParams = request.nextUrl.searchParams;
    const locationId = searchParams.get('locationId');
    
    // Get all staff
    const allStaff = StaffStorage.getStaff();
    
    // Filter by location if provided
    let filteredStaff = allStaff;
    if (locationId) {
      if (locationId === 'home') {
        filteredStaff = allStaff.filter(s => s.homeService === true || s.locations.includes('home'));
      } else {
        filteredStaff = allStaff.filter(s => s.locations.includes(locationId));
      }
    }
    
    // Return the staff data
    return NextResponse.json(
      { staff: filteredStaff },
      {
        status: 200,
        headers: {
          'Cache-Control': 'public, s-maxage=300, stale-while-revalidate=60',
        }
      }
    );
  } catch (error) {
    console.error('Error fetching staff:', error);
    return NextResponse.json(
      { error: 'Failed to fetch staff data' },
      { status: 500 }
    );
  }
}
