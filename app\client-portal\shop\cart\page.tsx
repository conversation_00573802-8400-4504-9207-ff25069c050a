"use client"

import { useState } from "react"
import Link from "next/link"
import Image from "next/image"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, <PERSON><PERSON><PERSON>nt, <PERSON>Footer, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Separator } from "@/components/ui/separator"
import { ClientPortalLayout } from "@/components/client-portal/client-portal-layout"
import {
  ShoppingBag,
  Trash2,
  ChevronLeft,
  Minus,
  Plus,
  CreditCard,
  Gift,
  ShieldCheck
} from "lucide-react"
import { useToast } from "@/components/ui/use-toast"
import { CurrencyDisplay } from "@/components/ui/currency-display"
import { useCurrency } from "@/lib/currency-provider"

// Mock cart items
const initialCartItems = [
  {
    id: "1",
    name: "Hydrating Shampoo",
    price: 24.99,
    image: "/product-1.jpg",
    quantity: 1
  },
  {
    id: "2",
    name: "Volumizing Conditioner",
    price: 22.99,
    image: "/product-2.jpg",
    quantity: 1
  },
  {
    id: "4",
    name: "Styling Mousse",
    price: 18.99,
    image: "/product-4.jpg",
    quantity: 2,
    isSale: true,
    salePrice: 15.99
  }
]

export default function CartPage() {
  const { toast } = useToast()
  const { formatCurrency } = useCurrency()
  const [cartItems, setCartItems] = useState(initialCartItems)
  const [promoCode, setPromoCode] = useState("")
  const [isApplyingPromo, setIsApplyingPromo] = useState(false)
  const [appliedPromo, setAppliedPromo] = useState<string | null>(null)
  const [discount, setDiscount] = useState(0)

  // Calculate cart totals
  const subtotal = cartItems.reduce((total, item) => {
    const itemPrice = item.isSale ? (item.salePrice || item.price) : item.price
    return total + (itemPrice * item.quantity)
  }, 0)

  const shipping = subtotal > 50 ? 0 : 5.99
  const tax = subtotal * 0.08 // 8% tax rate
  const total = subtotal + shipping + tax - discount

  const updateQuantity = (id: string, newQuantity: number) => {
    if (newQuantity < 1) return

    setCartItems(prev =>
      prev.map(item =>
        item.id === id ? { ...item, quantity: newQuantity } : item
      )
    )
  }

  const removeItem = (id: string) => {
    setCartItems(prev => prev.filter(item => item.id !== id))

    toast({
      title: "Item removed",
      description: "The item has been removed from your cart.",
    })
  }

  const handleApplyPromo = () => {
    if (!promoCode) return

    setIsApplyingPromo(true)

    // Simulate API call
    setTimeout(() => {
      if (promoCode.toUpperCase() === "WELCOME10") {
        setDiscount(subtotal * 0.1) // 10% discount
        setAppliedPromo(promoCode)

        toast({
          title: "Promo code applied",
          description: "10% discount has been applied to your order.",
        })
      } else {
        toast({
          title: "Invalid promo code",
          description: "The promo code you entered is invalid or expired.",
          variant: "destructive",
        })
      }

      setIsApplyingPromo(false)
    }, 1000)
  }

  const handleRemovePromo = () => {
    setAppliedPromo(null)
    setDiscount(0)
    setPromoCode("")

    toast({
      title: "Promo code removed",
      description: "The discount has been removed from your order.",
    })
  }

  return (
    <ClientPortalLayout>
      <div className="container mx-auto px-4 py-8">
        <div className="mb-8">
          <h1 className="text-2xl font-bold mb-2">Your Shopping Cart</h1>
          <p className="text-gray-600">
            Review your items and proceed to checkout
          </p>
        </div>

        {cartItems.length === 0 ? (
          <div className="text-center py-16 bg-gray-50 rounded-lg">
            <ShoppingBag className="h-16 w-16 text-gray-300 mx-auto mb-4" />
            <h2 className="text-xl font-medium mb-2">Your cart is empty</h2>
            <p className="text-gray-500 mb-8 max-w-md mx-auto">
              Looks like you haven't added any products to your cart yet.
            </p>
            <Button className="bg-pink-600 hover:bg-pink-700" asChild>
              <Link href="/client-portal/shop">
                Start Shopping
              </Link>
            </Button>
          </div>
        ) : (
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Cart Items */}
            <div className="lg:col-span-2">
              <Card>
                <CardHeader className="pb-3">
                  <CardTitle>Cart Items ({cartItems.length})</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-6">
                    {cartItems.map((item) => (
                      <div key={item.id} className="flex gap-4 pb-6 border-b last:border-0 last:pb-0">
                        <div className="relative w-20 h-20 rounded-md overflow-hidden flex-shrink-0">
                          <Image
                            src={item.image}
                            alt={item.name}
                            fill
                            className="object-cover"
                          />
                        </div>

                        <div className="flex-1">
                          <div className="flex justify-between">
                            <Link href={`/client-portal/shop/${item.id}`} className="font-medium hover:text-pink-600 transition-colors">
                              {item.name}
                            </Link>
                            <Button
                              variant="ghost"
                              size="icon"
                              className="h-8 w-8 text-gray-500 hover:text-red-500"
                              onClick={() => removeItem(item.id)}
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>

                          <div className="flex justify-between items-center mt-2">
                            <div className="flex border rounded-md">
                              <Button
                                variant="ghost"
                                size="icon"
                                className="h-8 w-8 rounded-none border-r"
                                onClick={() => updateQuantity(item.id, item.quantity - 1)}
                              >
                                <Minus className="h-3 w-3" />
                              </Button>
                              <div className="flex items-center justify-center w-10 text-center text-sm">
                                {item.quantity}
                              </div>
                              <Button
                                variant="ghost"
                                size="icon"
                                className="h-8 w-8 rounded-none border-l"
                                onClick={() => updateQuantity(item.id, item.quantity + 1)}
                              >
                                <Plus className="h-3 w-3" />
                              </Button>
                            </div>

                            <div className="text-right">
                              {item.isSale ? (
                                <div className="flex flex-col items-end">
                                  <span className="font-medium"><CurrencyDisplay amount={(item.salePrice || item.price) * item.quantity} /></span>
                                  <span className="text-xs text-gray-500 line-through"><CurrencyDisplay amount={item.price * item.quantity} /></span>
                                </div>
                              ) : (
                                <span className="font-medium"><CurrencyDisplay amount={item.price * item.quantity} /></span>
                              )}
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
                <CardFooter className="flex justify-between">
                  <Button variant="outline" asChild>
                    <Link href="/client-portal/shop">
                      <ChevronLeft className="mr-2 h-4 w-4" />
                      Continue Shopping
                    </Link>
                  </Button>

                  <Button
                    variant="ghost"
                    className="text-red-600 hover:text-red-700 hover:bg-red-50"
                    onClick={() => setCartItems([])}
                  >
                    <Trash2 className="mr-2 h-4 w-4" />
                    Clear Cart
                  </Button>
                </CardFooter>
              </Card>
            </div>

            {/* Order Summary */}
            <div>
              <Card>
                <CardHeader>
                  <CardTitle>Order Summary</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span>Subtotal</span>
                      <span><CurrencyDisplay amount={subtotal} /></span>
                    </div>

                    <div className="flex justify-between text-sm">
                      <span>Shipping</span>
                      <span>{shipping === 0 ? "Free" : <CurrencyDisplay amount={shipping} />}</span>
                    </div>

                    <div className="flex justify-between text-sm">
                      <span>Tax</span>
                      <span><CurrencyDisplay amount={tax} /></span>
                    </div>

                    {discount > 0 && (
                      <div className="flex justify-between text-sm text-green-600">
                        <span>Discount ({appliedPromo})</span>
                        <span>-<CurrencyDisplay amount={discount} /></span>
                      </div>
                    )}
                  </div>

                  <Separator />

                  <div className="flex justify-between font-medium">
                    <span>Total</span>
                    <span><CurrencyDisplay amount={total} /></span>
                  </div>

                  <div className="pt-4">
                    <div className="flex gap-2 mb-4">
                      <Input
                        placeholder="Promo code"
                        value={promoCode}
                        onChange={(e) => setPromoCode(e.target.value)}
                        disabled={!!appliedPromo}
                      />
                      {appliedPromo ? (
                        <Button
                          variant="outline"
                          onClick={handleRemovePromo}
                        >
                          Remove
                        </Button>
                      ) : (
                        <Button
                          variant="outline"
                          onClick={handleApplyPromo}
                          disabled={isApplyingPromo || !promoCode}
                        >
                          {isApplyingPromo ? "Applying..." : "Apply"}
                        </Button>
                      )}
                    </div>

                    <Button className="w-full bg-pink-600 hover:bg-pink-700 mb-4" asChild>
                      <Link href="/client-portal/shop/checkout">
                        <CreditCard className="mr-2 h-4 w-4" />
                        Proceed to Checkout
                      </Link>
                    </Button>

                    <div className="space-y-3 text-sm text-gray-500">
                      <div className="flex items-center gap-2">
                        <ShieldCheck className="h-4 w-4 text-gray-400" />
                        <span>Secure checkout</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <Gift className="h-4 w-4 text-gray-400" />
                        <span>Free shipping on orders over <CurrencyDisplay amount={50} /></span>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <div className="mt-6 bg-gray-50 p-4 rounded-lg">
                <h3 className="font-medium mb-2">We Accept</h3>
                <div className="flex gap-2">
                  <div className="w-12 h-8 bg-gray-200 rounded"></div>
                  <div className="w-12 h-8 bg-gray-200 rounded"></div>
                  <div className="w-12 h-8 bg-gray-200 rounded"></div>
                  <div className="w-12 h-8 bg-gray-200 rounded"></div>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </ClientPortalLayout>
  )
}
