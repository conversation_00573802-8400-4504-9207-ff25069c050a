"use client"

import { useState, useEffect } from "react"
import { Card, CardContent } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Star, Heart, ShoppingBag, Calendar } from "lucide-react"
import Link from "next/link"
import Image from "next/image"
import { useToast } from "@/components/ui/use-toast"
import { useCurrency } from "@/lib/currency-provider"
import { CurrencyDisplay } from "@/components/ui/currency-display"

interface PersonalizedRecommendationsProps {
  clientId: string
  clientPreferences?: {
    preferredServices?: string[]
    preferredProducts?: string[]
    preferredStylists?: string[]
  }
  pastAppointments?: any[]
  pastPurchases?: any[]
}

export function PersonalizedRecommendations({
  clientId,
  clientPreferences = {},
  pastAppointments = [],
  pastPurchases = []
}: PersonalizedRecommendationsProps) {
  const { toast } = useToast()
  const { formatCurrency } = useCurrency()
  const [recommendedServices, setRecommendedServices] = useState<any[]>([])
  const [recommendedProducts, setRecommendedProducts] = useState<any[]>([])
  const [recommendedStylists, setRecommendedStylists] = useState<any[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const fetchRecommendations = async () => {
      try {
        // In a real app, this would be an API call to get personalized recommendations
        // For now, we'll use mock data

        // Mock recommended services based on preferences and past appointments
        const mockServices = [
          {
            id: "s1",
            name: "Haircut & Style",
            price: 65.00,
            duration: 60,
            image: "/service-1.jpg",
            category: "Hair",
            rating: 4.9,
            reason: "Based on your past appointments"
          },
          {
            id: "s3",
            name: "Color & Highlights",
            price: 120.00,
            duration: 120,
            image: "/service-3.jpg",
            category: "Color",
            rating: 4.8,
            reason: "Popular with clients like you"
          },
          {
            id: "s5",
            name: "Deep Conditioning Treatment",
            price: 45.00,
            duration: 30,
            image: "/service-5.jpg",
            category: "Hair",
            rating: 4.7,
            reason: "Recommended for your hair type"
          },
          {
            id: "s8",
            name: "Blowout",
            price: 55.00,
            duration: 45,
            image: "/service-8.jpg",
            category: "Hair",
            rating: 4.6,
            reason: "Pairs well with your recent services"
          }
        ]

        // Mock recommended products based on preferences and past purchases
        const mockProducts = [
          {
            id: "p1",
            name: "Hydrating Shampoo",
            price: 24.99,
            image: "/product-1.jpg",
            category: "Hair Care",
            rating: 4.8,
            stock: 15,
            reason: "Based on your hair type"
          },
          {
            id: "p2",
            name: "Volumizing Conditioner",
            price: 22.99,
            image: "/product-2.jpg",
            category: "Hair Care",
            rating: 4.6,
            stock: 12,
            reason: "Pairs well with your shampoo"
          },
          {
            id: "p4",
            name: "Styling Mousse",
            price: 18.99,
            image: "/product-4.jpg",
            category: "Styling",
            rating: 4.5,
            stock: 8,
            isSale: true,
            salePrice: 15.99,
            reason: "Recommended by your stylist"
          }
        ]

        // Mock recommended stylists based on preferences and past appointments
        const mockStylists = [
          {
            id: "1",
            name: "Emma Johnson",
            role: "Senior Stylist",
            image: "/staff-1.jpg",
            rating: 4.9,
            specialties: ["Haircuts", "Color"],
            reason: "Your most visited stylist"
          },
          {
            id: "2",
            name: "Michael Chen",
            role: "Color Specialist",
            image: "/staff-2.jpg",
            rating: 4.8,
            specialties: ["Balayage", "Highlights"],
            reason: "Specializes in your preferred services"
          },
          {
            id: "5",
            name: "Sophia Rodriguez",
            role: "Stylist",
            image: "/staff-5.jpg",
            rating: 4.7,
            specialties: ["Curly Hair", "Natural Styles"],
            reason: "Highly rated for your hair type"
          }
        ]

        setRecommendedServices(mockServices)
        setRecommendedProducts(mockProducts)
        setRecommendedStylists(mockStylists)
        setLoading(false)
      } catch (error) {
        console.error("Error fetching recommendations:", error)
        setLoading(false)
      }
    }

    fetchRecommendations()
    // Only depend on clientId which is a string, not the object references
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [clientId])

  const addToFavorites = (item: any, type: string) => {
    toast({
      title: "Added to favorites",
      description: `${item.name} has been added to your favorites.`,
    })
  }

  const addToCart = (product: any) => {
    toast({
      title: "Added to cart",
      description: `${product.name} has been added to your cart.`,
    })
  }

  if (loading) {
    return (
      <div className="space-y-4">
        {[1, 2, 3].map((i) => (
          <Card key={i} className="overflow-hidden">
            <CardContent className="p-0">
              <div className="animate-pulse">
                <div className="h-40 bg-gray-200"></div>
                <div className="p-4 space-y-3">
                  <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                  <div className="h-4 bg-gray-200 rounded w-1/2"></div>
                  <div className="h-8 bg-gray-200 rounded w-full mt-4"></div>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    )
  }

  return (
    <div className="space-y-8">
      {/* Recommended Services */}
      {recommendedServices.length > 0 && (
        <div>
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-lg font-bold">Recommended Services</h3>
            <Button variant="link" size="sm" asChild>
              <Link href="/client-portal/services" className="text-pink-600">
                View All Services
              </Link>
            </Button>
          </div>

          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
            {recommendedServices.map((service) => (
              <Card key={service.id} className="overflow-hidden group hover:shadow-md transition-shadow">
                <CardContent className="p-0">
                  <div className="relative h-40 w-full bg-gray-100">
                    <Image
                      src={service.image}
                      alt={service.name}
                      fill
                      className="object-cover"
                    />
                    <div className="absolute inset-0 bg-black/40 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center">
                      <Button
                        className="bg-pink-600 hover:bg-pink-700"
                        size="sm"
                        asChild
                      >
                        <Link href={`/client-portal/appointments/book?serviceId=${service.id}`}>
                          <Calendar className="mr-2 h-4 w-4" />
                          Book Now
                        </Link>
                      </Button>
                    </div>
                  </div>

                  <div className="p-4">
                    <div className="flex justify-between items-start">
                      <div>
                        <h4 className="font-medium">{service.name}</h4>
                        <div className="flex items-center gap-1 mt-1">
                          <div className="flex text-amber-400">
                            {[...Array(5)].map((_, i) => (
                              <Star
                                key={i}
                                className={`h-3.5 w-3.5 ${i < Math.floor(service.rating) ? "fill-amber-400" : "fill-gray-200"}`}
                              />
                            ))}
                          </div>
                          <span className="text-xs text-gray-500">{service.rating}</span>
                        </div>
                        <p className="text-sm text-gray-500 mt-1">{service.reason}</p>
                      </div>
                      <Button
                        variant="ghost"
                        size="icon"
                        className="h-8 w-8 text-gray-400 hover:text-pink-600"
                        onClick={() => addToFavorites(service, 'service')}
                      >
                        <Heart className="h-4 w-4" />
                        <span className="sr-only">Add to favorites</span>
                      </Button>
                    </div>

                    <div className="flex justify-between items-center mt-3">
                      <p className="font-bold"><CurrencyDisplay amount={service.price} /></p>
                      <p className="text-xs text-gray-500">{service.duration} min</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      )}

      {/* Recommended Products */}
      {recommendedProducts.length > 0 && (
        <div>
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-lg font-bold">Recommended Products</h3>
            <Button variant="link" size="sm" asChild>
              <Link href="/client-portal/shop" className="text-pink-600">
                View All Products
              </Link>
            </Button>
          </div>

          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
            {recommendedProducts.map((product) => (
              <Card key={product.id} className="overflow-hidden group hover:shadow-md transition-shadow">
                <CardContent className="p-0">
                  <div className="relative h-40 w-full bg-gray-100">
                    <Image
                      src={product.image}
                      alt={product.name}
                      fill
                      className="object-cover"
                    />
                    {product.isSale && (
                      <Badge className="absolute top-2 left-2 bg-red-500">Sale</Badge>
                    )}
                    <div className="absolute inset-0 bg-black/40 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center">
                      <Button
                        className="bg-pink-600 hover:bg-pink-700"
                        size="sm"
                        onClick={() => addToCart(product)}
                      >
                        <ShoppingBag className="mr-2 h-4 w-4" />
                        Add to Cart
                      </Button>
                    </div>
                  </div>

                  <div className="p-4">
                    <div className="flex justify-between items-start">
                      <div>
                        <h4 className="font-medium">{product.name}</h4>
                        <div className="flex items-center gap-1 mt-1">
                          <div className="flex text-amber-400">
                            {[...Array(5)].map((_, i) => (
                              <Star
                                key={i}
                                className={`h-3.5 w-3.5 ${i < Math.floor(product.rating) ? "fill-amber-400" : "fill-gray-200"}`}
                              />
                            ))}
                          </div>
                          <span className="text-xs text-gray-500">{product.rating}</span>
                        </div>
                        <p className="text-sm text-gray-500 mt-1">{product.reason}</p>
                      </div>
                      <Button
                        variant="ghost"
                        size="icon"
                        className="h-8 w-8 text-gray-400 hover:text-pink-600"
                        onClick={() => addToFavorites(product, 'product')}
                      >
                        <Heart className="h-4 w-4" />
                        <span className="sr-only">Add to favorites</span>
                      </Button>
                    </div>

                    <div className="flex items-center mt-3">
                      {product.isSale ? (
                        <>
                          <p className="font-bold"><CurrencyDisplay amount={product.salePrice} /></p>
                          <p className="text-sm text-gray-500 line-through ml-2"><CurrencyDisplay amount={product.price} /></p>
                        </>
                      ) : (
                        <p className="font-bold"><CurrencyDisplay amount={product.price} /></p>
                      )}
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      )}

      {/* Recommended Stylists */}
      {recommendedStylists.length > 0 && (
        <div>
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-lg font-bold">Recommended Stylists</h3>
            <Button variant="link" size="sm" asChild>
              <Link href="/client-portal/stylists" className="text-pink-600">
                View All Stylists
              </Link>
            </Button>
          </div>

          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
            {recommendedStylists.map((stylist) => (
              <Card key={stylist.id} className="overflow-hidden group hover:shadow-md transition-shadow">
                <CardContent className="p-0">
                  <div className="flex">
                    <div className="relative h-32 w-32 flex-shrink-0 bg-gray-100">
                      <Image
                        src={stylist.image}
                        alt={stylist.name}
                        fill
                        className="object-cover"
                      />
                    </div>

                    <div className="p-4 flex-1">
                      <div className="flex justify-between items-start">
                        <div>
                          <h4 className="font-medium">{stylist.name}</h4>
                          <p className="text-sm text-gray-500">{stylist.role}</p>
                          <div className="flex items-center gap-1 mt-1">
                            <div className="flex text-amber-400">
                              {[...Array(5)].map((_, i) => (
                                <Star
                                  key={i}
                                  className={`h-3.5 w-3.5 ${i < Math.floor(stylist.rating) ? "fill-amber-400" : "fill-gray-200"}`}
                                />
                              ))}
                            </div>
                            <span className="text-xs text-gray-500">{stylist.rating}</span>
                          </div>
                        </div>
                        <Button
                          variant="ghost"
                          size="icon"
                          className="h-8 w-8 text-gray-400 hover:text-pink-600"
                          onClick={() => addToFavorites(stylist, 'stylist')}
                        >
                          <Heart className="h-4 w-4" />
                          <span className="sr-only">Add to favorites</span>
                        </Button>
                      </div>

                      <div className="mt-2">
                        <p className="text-xs text-gray-500">{stylist.reason}</p>
                        <div className="flex flex-wrap gap-1 mt-2">
                          {stylist.specialties.map((specialty: string) => (
                            <Badge key={specialty} variant="outline" className="text-xs">
                              {specialty}
                            </Badge>
                          ))}
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="p-3 bg-gray-50 border-t">
                    <Button
                      className="w-full bg-pink-600 hover:bg-pink-700"
                      size="sm"
                      asChild
                    >
                      <Link href={`/client-portal/appointments/book?staffId=${stylist.id}`}>
                        Book with {stylist.name.split(" ")[0]}
                      </Link>
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      )}
    </div>
  )
}
