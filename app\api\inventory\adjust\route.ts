import { NextResponse } from "next/server"
import { productsRepository } from "@/lib/db"

export async function POST(request: Request) {
  try {
    const data = await request.json()

    // Validate required fields
    if (!data.productId || !data.locationId || !data.quantity || !data.adjustmentType || !data.reason) {
      return NextResponse.json({ error: "Missing required fields" }, { status: 400 })
    }

    const productId = Number.parseInt(data.productId)
    const locationId = Number.parseInt(data.locationId)
    let quantity = Number.parseInt(data.quantity)

    // If removing stock, make quantity negative
    if (data.adjustmentType === "remove") {
      quantity = -quantity
    }

    // Update inventory
    const updatedInventory = await productsRepository.updateInventory(productId, locationId, quantity)

    // Record inventory transaction
    const { query } = await import("@/lib/db")

    await query(
      `INSERT INTO inventory_transactions 
        (product_id, location_id, quantity, transaction_type, notes, created_by) 
       VALUES ($1, $2, $3, $4, $5, $6)`,
      [productId, locationId, quantity, data.reason, data.notes || null, data.userId || null],
    )

    return NextResponse.json({
      success: true,
      inventory: updatedInventory,
    })
  } catch (error) {
    console.error("Error adjusting inventory:", error)
    return NextResponse.json({ error: "Failed to adjust inventory" }, { status: 500 })
  }
}

