"use client"

import { useAuth } from "@/lib/auth-provider"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { CalendarIcon, DollarSign, Users, Clock } from "lucide-react"

export function StatsCards() {
  const { currentLocation } = useAuth()

  // In a real app, these would be fetched from an API based on the current location
  const stats = {
    appointments:
      currentLocation === "all" ? 42 : currentLocation === "loc1" ? 18 : currentLocation === "loc2" ? 14 : 10,
    revenue:
      currentLocation === "all" ? 2840 : currentLocation === "loc1" ? 1250 : currentLocation === "loc2" ? 890 : 700,
    newClients: currentLocation === "all" ? 12 : currentLocation === "loc1" ? 5 : currentLocation === "loc2" ? 4 : 3,
    utilization:
      currentLocation === "all" ? 78 : currentLocation === "loc1" ? 82 : currentLocation === "loc2" ? 75 : 74,
  }

  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Today's Appointments</CardTitle>
          <CalendarIcon className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{stats.appointments}</div>
          <p className="text-xs text-muted-foreground">+2 from yesterday</p>
        </CardContent>
      </Card>
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Today's Revenue</CardTitle>
          <DollarSign className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">${stats.revenue}</div>
          <p className="text-xs text-muted-foreground">+$340 from yesterday</p>
        </CardContent>
      </Card>
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">New Clients (Week)</CardTitle>
          <Users className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{stats.newClients}</div>
          <p className="text-xs text-muted-foreground">+2 from last week</p>
        </CardContent>
      </Card>
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Staff Utilization</CardTitle>
          <Clock className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{stats.utilization}%</div>
          <p className="text-xs text-muted-foreground">+4% from last week</p>
        </CardContent>
      </Card>
    </div>
  )
}

