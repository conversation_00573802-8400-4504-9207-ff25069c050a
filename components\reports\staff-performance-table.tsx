"use client"

import { useAuth } from "@/lib/auth-provider"
import type { DateRange } from "react-day-picker"
import { Progress } from "@/components/ui/progress"
import { CurrencyDisplay } from "@/components/ui/currency-display"

interface StaffPerformanceTableProps {
  dateRange?: DateRange
  limit?: number
}

export function StaffPerformanceTable({ dateRange, limit }: StaffPerformanceTableProps) {
  const { currentLocation } = useAuth()

  // Mock data - would be replaced with actual API calls
  const staffData = [
    {
      id: "1",
      name: "<PERSON>",
      appointments: 42,
      revenue: 4250.75,
      avgRating: 4.9,
      rebookRate: 85,
    },
    {
      id: "2",
      name: "<PERSON>",
      appointments: 38,
      revenue: 3875.5,
      avgRating: 4.8,
      rebookRate: 82,
    },
    {
      id: "3",
      name: "<PERSON>",
      appointments: 35,
      revenue: 3420.25,
      avgRating: 4.7,
      rebookRate: 78,
    },
    {
      id: "4",
      name: "<PERSON>",
      appointments: 32,
      revenue: 3150.0,
      avgRating: 4.9,
      rebookRate: 80,
    },
    {
      id: "5",
      name: "<PERSON>",
      appointments: 30,
      revenue: 2950.75,
      avgRating: 4.6,
      rebookRate: 75,
    },
    {
      id: "6",
      name: "<PERSON>",
      appointments: 28,
      revenue: 2750.5,
      avgRating: 4.8,
      rebookRate: 79,
    },
    {
      id: "7",
      name: "Thomas Lee",
      appointments: 25,
      revenue: 2450.25,
      avgRating: 4.7,
      rebookRate: 72,
    },
  ]

  // Limit the data if requested
  const displayData = limit ? staffData.slice(0, limit) : staffData

  return (
    <div className="rounded-md border">
      <table className="w-full">
        <thead>
          <tr className="border-b bg-muted/50">
            <th className="p-3 text-left font-medium">Staff Member</th>
            <th className="p-3 text-right font-medium">Appointments</th>
            <th className="p-3 text-right font-medium">Revenue</th>
            <th className="p-3 text-center font-medium">Rating</th>
            <th className="p-3 text-center font-medium">Rebook Rate</th>
          </tr>
        </thead>
        <tbody>
          {displayData.map((staff) => (
            <tr key={staff.id} className="border-b">
              <td className="p-3 font-medium">{staff.name}</td>
              <td className="p-3 text-right">{staff.appointments}</td>
              <td className="p-3 text-right"><CurrencyDisplay amount={staff.revenue} /></td>
              <td className="p-3 text-center">
                <div className="flex items-center justify-center">
                  <span className="mr-2">{staff.avgRating}</span>
                  <div className="flex">
                    {Array.from({ length: 5 }).map((_, i) => (
                      <svg
                        key={i}
                        className={`h-4 w-4 ${i < Math.floor(staff.avgRating) ? "text-primary fill-primary" : "text-muted"}`}
                        xmlns="http://www.w3.org/2000/svg"
                        viewBox="0 0 24 24"
                      >
                        <path d="M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2 9.19 8.63 2 9.24l5.46 4.73L5.82 21z" />
                      </svg>
                    ))}
                  </div>
                </div>
              </td>
              <td className="p-3">
                <div className="flex flex-col items-center gap-1">
                  <Progress value={staff.rebookRate} className="h-2 w-full" />
                  <span className="text-xs">{staff.rebookRate}%</span>
                </div>
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  )
}

