import { NextResponse } from "next/server"
import { servicesRepository } from "@/lib/db"

export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url)
    const locationId = searchParams.get("locationId")

    let services

    if (locationId) {
      services = await servicesRepository.findByLocation(Number.parseInt(locationId))
    } else {
      services = await servicesRepository.findAll()
    }

    return NextResponse.json({ services })
  } catch (error) {
    console.error("Error fetching services:", error)
    return NextResponse.json({ error: "Failed to fetch services" }, { status: 500 })
  }
}

export async function POST(request: Request) {
  try {
    const data = await request.json()

    // Validate required fields
    if (!data.name || !data.duration || !data.price) {
      return NextResponse.json({ error: "Missing required fields" }, { status: 400 })
    }

    const service = await servicesRepository.create({
      name: data.name,
      description: data.description || null,
      duration: Number.parseInt(data.duration),
      price: Number.parseFloat(data.price),
      category_id: data.category_id || null,
    })

    // If locations are provided, associate the service with locations
    if (data.locations && Array.isArray(data.locations) && data.locations.length > 0) {
      const { query } = await import("@/lib/db")

      for (const locationId of data.locations) {
        await query(
          `INSERT INTO service_locations (service_id, location_id, price) 
           VALUES ($1, $2, $3)`,
          [service.id, locationId, data.locationPrices?.[locationId] || data.price],
        )
      }
    }

    return NextResponse.json({ service })
  } catch (error) {
    console.error("Error creating service:", error)
    return NextResponse.json({ error: "Failed to create service" }, { status: 500 })
  }
}

