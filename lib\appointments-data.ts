// Shared appointment data model for both client portal and main app
export interface Appointment {
  id: string;
  clientId: string;
  clientName: string;
  staffId: string;
  staffName: string;
  service: string;
  serviceId?: string;
  date: string; // ISO date string
  duration: number; // in minutes
  status: AppointmentStatus;
  location: string;
  notes?: string;
  price?: number;
  createdAt?: string;
  updatedAt?: string;
  // Additional properties needed for calendar view
  statusHistory?: Array<{
    status: string;
    timestamp: string;
    updatedBy?: string;
  }>;
  type?: string; // For special appointment types like "blocked"
  title?: string; // For blocked time title
  blockType?: string; // For blocked time type (break, meeting, etc.)
  additionalServices?: Array<{
    id?: string;
    name: string;
    price?: number;
    duration?: number;
    staffId?: string;
  }>;
  products?: Array<{
    id?: string;
    name: string;
    price?: number;
    quantity?: number;
  }>;
  isAdditionalService?: boolean; // Flag for additional services
}

export type AppointmentStatus =
  | "pending"
  | "confirmed"
  | "checked-in"
  | "arrived"
  | "service-started"
  | "completed"
  | "cancelled"
  | "no-show"
  | "blocked";

// Function to create a new appointment with all properties needed for calendar view
export function createAppointment(appointment: Omit<Appointment, "id" | "status" | "createdAt" | "updatedAt">): any {
  return {
    ...appointment,
    id: `a${Date.now()}`,
    status: "pending",
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    // Additional properties needed for calendar view
    statusHistory: [
      {
        status: "pending",
        timestamp: new Date().toISOString(),
        updatedBy: appointment.notes?.includes("client portal") ? "Client Portal" : "Staff"
      }
    ],
    type: "appointment", // Distinguish from blocked time
    additionalServices: [], // Initialize empty array for additional services
    products: [] // Initialize empty array for products
  };
}

// Function to update appointment status
export function updateAppointmentStatus(appointment: Appointment, newStatus: AppointmentStatus): Appointment {
  return {
    ...appointment,
    status: newStatus,
    updatedAt: new Date().toISOString()
  };
}

// Mock appointments data (extended from existing mock data)
export const appointments = [
  // Today's appointments
  {
    id: "a1",
    clientId: "ed1",
    clientName: "Emily Davis",
    staffId: "1",
    staffName: "Emma Johnson",
    service: "Haircut & Style",
    serviceId: "1",
    date: new Date().toISOString(), // Today
    duration: 60,
    status: "confirmed" as AppointmentStatus,
    location: "loc1",
    price: 75,
    bookingReference: "VH-20250524-1001",
    createdAt: "2025-03-25T14:30:00",
    updatedAt: "2025-03-25T14:35:00"
  },
  // Upcoming appointments
  {
    id: "a1b",
    clientId: "ed1",
    clientName: "Emily Davis",
    staffId: "2",
    staffName: "Michael Chen",
    service: "Color & Highlights",
    serviceId: "2",
    date: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(), // Next week
    duration: 120,
    status: "confirmed" as AppointmentStatus,
    location: "loc1",
    price: 150,
    bookingReference: "VH-20250531-1002",
    createdAt: "2025-03-25T14:30:00",
    updatedAt: "2025-03-25T14:35:00"
  },
  {
    id: "a1c",
    clientId: "ed1",
    clientName: "Emily Davis",
    staffId: "3",
    staffName: "Sophia Rodriguez",
    service: "Manicure",
    serviceId: "8",
    date: new Date(Date.now() + 14 * 24 * 60 * 60 * 1000).toISOString(), // Two weeks from now
    duration: 45,
    status: "pending" as AppointmentStatus,
    location: "loc2",
    price: 45,
    bookingReference: "VH-20250607-1003",
    createdAt: "2025-03-25T14:30:00",
    updatedAt: "2025-03-25T14:35:00"
  },
  // Past appointments
  {
    id: "a1d",
    clientId: "ed1",
    clientName: "Emily Davis",
    staffId: "1",
    staffName: "Emma Johnson",
    service: "Deep Conditioning",
    serviceId: "5",
    date: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(), // Last week
    duration: 30,
    status: "completed" as AppointmentStatus,
    location: "loc1",
    price: 45,
    bookingReference: "VH-20250517-1004",
    createdAt: "2025-03-10T14:30:00",
    updatedAt: "2025-03-17T14:35:00"
  },
  {
    id: "a1e",
    clientId: "ed1",
    clientName: "Emily Davis",
    staffId: "2",
    staffName: "Michael Chen",
    service: "Blowout",
    serviceId: "4",
    date: new Date(Date.now() - 14 * 24 * 60 * 60 * 1000).toISOString(), // Two weeks ago
    duration: 45,
    status: "completed" as AppointmentStatus,
    location: "loc1",
    price: 65,
    bookingReference: "VH-20250510-1005",
    createdAt: "2025-03-05T14:30:00",
    updatedAt: "2025-03-10T14:35:00"
  },
  {
    id: "a2",
    clientId: "jw2",
    clientName: "James Wilson",
    staffId: "2",
    staffName: "Michael Chen",
    service: "Color & Highlights",
    serviceId: "2",
    date: "2025-04-02T11:00:00",
    duration: 120,
    status: "confirmed" as AppointmentStatus,
    location: "loc1",
    price: 150,
    createdAt: "2025-03-26T09:15:00",
    updatedAt: "2025-03-26T09:20:00"
  },
  {
    id: "a3",
    clientId: "om3",
    clientName: "Olivia Martinez",
    staffId: "3",
    staffName: "Sophia Rodriguez",
    service: "Manicure & Pedicure",
    serviceId: "4",
    date: "2025-04-02T13:00:00",
    duration: 90,
    status: "checked-in" as AppointmentStatus,
    location: "loc2",
    price: 95,
    createdAt: "2025-03-27T16:45:00",
    updatedAt: "2025-04-02T12:55:00"
  },
  {
    id: "a4",
    clientId: "rj4",
    clientName: "Robert Johnson",
    staffId: "1",
    staffName: "Emma Johnson",
    service: "Men's Haircut",
    serviceId: "3",
    date: "2025-04-02T14:30:00",
    duration: 45,
    status: "pending" as AppointmentStatus,
    location: "loc1",
    price: 55,
    createdAt: "2025-03-28T10:30:00",
    updatedAt: "2025-03-28T10:30:00"
  },
  {
    id: "a5",
    clientId: "sl5",
    clientName: "Sophia Lee",
    staffId: "2",
    staffName: "Michael Chen",
    service: "Facial Treatment",
    serviceId: "6",
    date: "2025-04-03T11:00:00",
    duration: 60,
    status: "confirmed" as AppointmentStatus,
    location: "loc3",
    price: 85,
    createdAt: "2025-03-29T14:20:00",
    updatedAt: "2025-03-29T14:25:00"
  },
  {
    id: "a6",
    clientId: "ed1",
    clientName: "Emily Davis",
    staffId: "3",
    staffName: "Sophia Rodriguez",
    service: "Blowout",
    serviceId: "7",
    date: "2025-04-05T15:00:00",
    duration: 45,
    status: "confirmed" as AppointmentStatus,
    location: "loc1",
    price: 65,
    createdAt: "2025-03-30T09:10:00",
    updatedAt: "2025-03-30T09:15:00"
  }
];
