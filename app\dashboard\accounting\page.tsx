"use client"

import { useState } from "react"
import { useAuth } from "@/lib/auth-provider"
import { useSearchParams } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { DatePickerWithRange } from "@/components/date-range-picker"
import { FinancialOverview } from "@/components/accounting/financial-overview"
import { Transactions } from "@/components/accounting/transactions"
import { Expenses } from "@/components/accounting/expenses"
import { Payroll } from "@/components/accounting/payroll"
import { Reports } from "@/components/accounting/reports"
import { NewTransactionDialog } from "@/components/accounting/new-transaction-dialog"
import { NewExpenseDialog } from "@/components/accounting/new-expense-dialog"
import { Plus, Search, FileDown, Printer, CalendarIcon } from "lucide-react"
import { subDays } from "date-fns"
import type { DateRange } from "react-day-picker"
import { TransactionProvider } from "@/lib/transaction-provider"

export default function AccountingPage() {
  const { user, currentLocation } = useAuth()
  const [search, setSearch] = useState("")
  const [isTransactionDialogOpen, setIsTransactionDialogOpen] = useState(false)
  const [isExpenseDialogOpen, setIsExpenseDialogOpen] = useState(false)
  const [dateRange, setDateRange] = useState<DateRange | undefined>({
    from: subDays(new Date(), 30),
    to: new Date(),
  })
  const [singleDate, setSingleDate] = useState<Date | undefined>(new Date())
  const [dateMode, setDateMode] = useState<"single" | "range">("range")

  // Get the active tab from URL params or default to "overview"
  const searchParams = useSearchParams()
  const activeTab = searchParams?.get("tab") || "overview"

  // Only certain roles can access this page
  if (user?.role !== "super_admin" && user?.role !== "org_admin" && user?.role !== "location_manager") {
    return (
      <div className="flex h-full items-center justify-center">
        <Card className="max-w-md">
          <CardHeader>
            <CardTitle>Access Denied</CardTitle>
            <p className="text-muted-foreground">You don't have permission to view the accounting page.</p>
          </CardHeader>
        </Card>
      </div>
    )
  }

  return (
    <TransactionProvider>
      <div className="space-y-6">
      <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">Accounting</h2>
          <p className="text-muted-foreground">Manage your salon's finances, expenses, and reports.</p>
        </div>
        <div className="flex items-center gap-2">
          <DatePickerWithRange
            dateRange={dateRange}
            singleDate={singleDate}
            mode={dateMode}
            onDateRangeChange={setDateRange}
            onSingleDateChange={setSingleDate}
            onModeChange={setDateMode}
          />
          <Button variant="outline">
            <FileDown className="mr-2 h-4 w-4" />
            Export
          </Button>
          <Button variant="outline" size="icon">
            <Printer className="h-4 w-4" />
          </Button>
        </div>
      </div>

      <Tabs defaultValue={activeTab} className="w-full">
        <TabsList className="mb-4">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="transactions">Transactions</TabsTrigger>
          <TabsTrigger value="expenses">Expenses</TabsTrigger>
          <TabsTrigger value="payroll">Payroll</TabsTrigger>
          <TabsTrigger value="reports">Reports</TabsTrigger>
        </TabsList>

        <TabsContent value="overview">
          <FinancialOverview dateRange={dateRange} />
        </TabsContent>

        <TabsContent value="transactions">
          <div className="mb-4 flex justify-between items-center">
            <div className="relative">
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search transactions..."
                value={search}
                onChange={(e) => setSearch(e.target.value)}
                className="pl-8 w-[200px] md:w-[300px]"
              />
            </div>

            <Button onClick={() => setIsTransactionDialogOpen(true)}>
              <Plus className="mr-2 h-4 w-4" />
              New Transaction
            </Button>
          </div>
          <Transactions
            search={search}
            dateRange={dateRange}
            singleDate={singleDate}
            dateMode={dateMode}
            selectedLocation={currentLocation}
          />
        </TabsContent>

        <TabsContent value="expenses">
          <div className="mb-4 flex justify-between items-center">
            <div className="relative">
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search expenses..."
                value={search}
                onChange={(e) => setSearch(e.target.value)}
                className="pl-8 w-[200px] md:w-[300px]"
              />
            </div>
            <Button onClick={() => setIsExpenseDialogOpen(true)}>
              <Plus className="mr-2 h-4 w-4" />
              New Expense
            </Button>
          </div>
          <Expenses search={search} dateRange={dateRange} selectedLocation={currentLocation} />
        </TabsContent>

        <TabsContent value="payroll">
          <Payroll dateRange={dateRange} />
        </TabsContent>

        <TabsContent value="reports">
          <Reports dateRange={dateRange} />
        </TabsContent>
      </Tabs>

      <NewTransactionDialog open={isTransactionDialogOpen} onOpenChange={setIsTransactionDialogOpen} />
      <NewExpenseDialog open={isExpenseDialogOpen} onOpenChange={setIsExpenseDialogOpen} />
    </div>
    </TransactionProvider>
  )
}

