"use client"

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card"
import { Avatar, AvatarFallback } from "@/components/ui/avatar"
import { Progress } from "@/components/ui/progress"
import { Tabs, <PERSON><PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { CurrencyDisplay } from "@/components/ui/currency-display"

interface StaffPerformanceProps {
  fullView?: boolean
}

export function StaffPerformance({ fullView = false }: StaffPerformanceProps) {
  const staffData = [
    {
      id: "ej",
      name: "<PERSON>",
      role: "Senior Stylist",
      revenue: 4250,
      appointments: 45,
      utilization: 92,
      color: "bg-purple-100 text-purple-800",
    },
    {
      id: "mc",
      name: "<PERSON>",
      role: "Colorist",
      revenue: 3800,
      appointments: 38,
      utilization: 88,
      color: "bg-blue-100 text-blue-800",
    },
    {
      id: "sr",
      name: "<PERSON>",
      role: "<PERSON><PERSON> Technician",
      revenue: 2950,
      appointments: 52,
      utilization: 85,
      color: "bg-pink-100 text-pink-800",
    },
    {
      id: "dk",
      name: "<PERSON>",
      role: "<PERSON>yl<PERSON>",
      revenue: 2600,
      appointments: 32,
      utilization: 78,
      color: "bg-orange-100 text-orange-800",
    },
    {
      id: "jl",
      name: "Jessica Lee",
      role: "Esthetician",
      revenue: 2400,
      appointments: 28,
      utilization: 75,
      color: "bg-green-100 text-green-800",
    },
  ]

  if (fullView) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="text-xl font-bold">Staff Performance</CardTitle>
          <p className="text-sm text-muted-foreground">Detailed performance metrics for all staff members.</p>
        </CardHeader>
        <CardContent>
          <Tabs defaultValue="revenue">
            <TabsList className="mb-4">
              <TabsTrigger value="revenue">Revenue</TabsTrigger>
              <TabsTrigger value="appointments">Appointments</TabsTrigger>
              <TabsTrigger value="utilization">Utilization</TabsTrigger>
            </TabsList>

            <TabsContent value="revenue">
              <div className="space-y-6">
                {staffData.map((staff) => (
                  <div key={staff.id} className="space-y-2">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        <Avatar className={`h-8 w-8 ${staff.color}`}>
                          <AvatarFallback>{staff.id.toUpperCase()}</AvatarFallback>
                        </Avatar>
                        <div>
                          <p className="font-medium">{staff.name}</p>
                          <p className="text-xs text-muted-foreground">{staff.role}</p>
                        </div>
                      </div>
                      <div className="text-right">
                        <p className="font-medium"><CurrencyDisplay amount={staff.revenue} /></p>
                      </div>
                    </div>
                    <Progress value={(staff.revenue / 5000) * 100} className="h-2" />
                  </div>
                ))}
              </div>
            </TabsContent>

            <TabsContent value="appointments">
              <div className="space-y-6">
                {staffData.map((staff) => (
                  <div key={staff.id} className="space-y-2">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        <Avatar className={`h-8 w-8 ${staff.color}`}>
                          <AvatarFallback>{staff.id.toUpperCase()}</AvatarFallback>
                        </Avatar>
                        <div>
                          <p className="font-medium">{staff.name}</p>
                          <p className="text-xs text-muted-foreground">{staff.role}</p>
                        </div>
                      </div>
                      <div className="text-right">
                        <p className="font-medium">{staff.appointments} appointments</p>
                      </div>
                    </div>
                    <Progress value={(staff.appointments / 60) * 100} className="h-2" />
                  </div>
                ))}
              </div>
            </TabsContent>

            <TabsContent value="utilization">
              <div className="space-y-6">
                {staffData.map((staff) => (
                  <div key={staff.id} className="space-y-2">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        <Avatar className={`h-8 w-8 ${staff.color}`}>
                          <AvatarFallback>{staff.id.toUpperCase()}</AvatarFallback>
                        </Avatar>
                        <div>
                          <p className="font-medium">{staff.name}</p>
                          <p className="text-xs text-muted-foreground">{staff.role}</p>
                        </div>
                      </div>
                      <div className="text-right">
                        <p className="font-medium">{staff.utilization}%</p>
                      </div>
                    </div>
                    <Progress value={staff.utilization} className="h-2" />
                  </div>
                ))}
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader className="pb-2">
        <CardTitle className="text-xl font-bold">Staff Performance</CardTitle>
        <p className="text-sm text-muted-foreground">Top performing staff members.</p>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {staffData.slice(0, 3).map((staff) => (
            <div key={staff.id} className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <Avatar className={`h-8 w-8 ${staff.color}`}>
                  <AvatarFallback>{staff.id.toUpperCase()}</AvatarFallback>
                </Avatar>
                <div>
                  <p className="font-medium">{staff.name}</p>
                  <p className="text-xs text-muted-foreground">{staff.role}</p>
                </div>
              </div>
              <div className="text-right">
                <p className="font-medium"><CurrencyDisplay amount={staff.revenue} /></p>
                <p className="text-xs text-muted-foreground">{staff.appointments} appts</p>
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  )
}

