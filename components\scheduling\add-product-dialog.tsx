"use client"

import { useState } from "react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>er, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Footer } from "@/components/ui/dialog"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Label } from "@/components/ui/label"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { useToast } from "@/components/ui/use-toast"

interface AddProductDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  bookingId: string
  onProductAdded: (bookingId: string, product: any) => void
}

// Mock products for demonstration
const mockProducts = [
  { id: "prod1", name: "Shampoo", price: 24.99, category: "Hair Care" },
  { id: "prod2", name: "Conditioner", price: 22.99, category: "Hair Care" },
  { id: "prod3", name: "Hair Spray", price: 18.99, category: "Styl<PERSON>" },
  { id: "prod4", name: "<PERSON>yling Gel", price: 15.99, category: "Styling" },
  { id: "prod5", name: "Hair Mask", price: 29.99, category: "Treatment" },
  { id: "prod6", name: "Heat Protectant", price: 19.99, category: "Styling" },
  { id: "prod7", name: "Hair Oil", price: 27.99, category: "Treatment" },
  { id: "prod8", name: "Dry Shampoo", price: 16.99, category: "Hair Care" },
  { id: "prod9", name: "Curl Cream", price: 21.99, category: "Styling" },
  { id: "prod10", name: "Scalp Treatment", price: 34.99, category: "Treatment" },
]

// Product categories
const productCategories = ["Hair Care", "Styling", "Treatment"]

export function AddProductDialog({ open, onOpenChange, bookingId, onProductAdded }: AddProductDialogProps) {
  const { toast } = useToast()
  const [selectedCategory, setSelectedCategory] = useState("")
  const [selectedProduct, setSelectedProduct] = useState("")
  const [quantity, setQuantity] = useState(1)
  const [isSubmitting, setIsSubmitting] = useState(false)

  // Filter products by category
  const filteredProducts = selectedCategory ? mockProducts.filter((p) => p.category === selectedCategory) : mockProducts

  const handleSubmit = async () => {
    if (!selectedProduct) {
      toast({
        variant: "destructive",
        title: "Missing information",
        description: "Please select a product.",
      })
      return
    }

    if (quantity < 1) {
      toast({
        variant: "destructive",
        title: "Invalid quantity",
        description: "Quantity must be at least 1.",
      })
      return
    }

    setIsSubmitting(true)

    try {
      // Find the selected product details
      const productDetails = mockProducts.find((p) => p.id === selectedProduct)

      if (!productDetails) {
        throw new Error("Product not found")
      }

      // Create the product object
      const newProduct = {
        id: `product-${Date.now()}`,
        type: "product",
        name: productDetails.name,
        price: productDetails.price * quantity,
        quantity: quantity,
        unitPrice: productDetails.price,
      }

      // Call the callback to add the product
      onProductAdded(bookingId, newProduct)

      toast({
        title: "Product added",
        description: `${quantity} x ${productDetails.name} has been added to the booking.`,
      })

      // Reset form and close dialog
      setSelectedCategory("")
      setSelectedProduct("")
      setQuantity(1)
      onOpenChange(false)
    } catch (error) {
      console.error("Failed to add product:", error)
      toast({
        variant: "destructive",
        title: "Error",
        description: "Failed to add the product. Please try again.",
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>Add Product</DialogTitle>
        </DialogHeader>

        <div className="grid gap-4 py-4">
          <div className="space-y-2">
            <Label htmlFor="category">Product Category</Label>
            <Select value={selectedCategory} onValueChange={setSelectedCategory}>
              <SelectTrigger id="category">
                <SelectValue placeholder="Select a category" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Categories</SelectItem>
                {productCategories.map((category) => (
                  <SelectItem key={category} value={category}>
                    {category}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label htmlFor="product">Product</Label>
            <Select value={selectedProduct} onValueChange={setSelectedProduct}>
              <SelectTrigger id="product">
                <SelectValue placeholder="Select a product" />
              </SelectTrigger>
              <SelectContent>
                {filteredProducts.map((product) => (
                  <SelectItem key={product.id} value={product.id}>
                    {product.name} - ${product.price.toFixed(2)}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label htmlFor="quantity">Quantity</Label>
            <Input
              id="quantity"
              type="number"
              min="1"
              value={quantity}
              onChange={(e) => setQuantity(Number.parseInt(e.target.value) || 1)}
            />
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Cancel
          </Button>
          <Button onClick={handleSubmit} disabled={isSubmitting} className="bg-black text-white hover:bg-gray-800">
            {isSubmitting ? "Adding..." : "Add Product"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}

