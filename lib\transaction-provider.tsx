"use client"

import React, { createContext, useContext, useState, useEffect } from "react"
import { v4 as uuidv4 } from "uuid"
import { 
  Transaction, 
  TransactionCreate, 
  TransactionFilter, 
  TransactionSource, 
  TransactionStatus, 
  TransactionType 
} from "./transaction-types"
import { mockTransactions } from "./mock-data"
import { format, isAfter, isBefore, isSameDay, parseISO } from "date-fns"

// Convert mock transactions to our new format
const convertedMockTransactions: Transaction[] = mockTransactions.map(tx => ({
  id: tx.id,
  date: new Date(tx.date),
  clientName: tx.client,
  type: tx.type.toLowerCase().includes("service") ? TransactionType.INCOME : TransactionType.EXPENSE,
  category: tx.type,
  description: tx.description,
  amount: tx.amount,
  paymentMethod: tx.paymentMethod === "Credit Card" ? "credit_card" : 
                 tx.paymentMethod === "Cash" ? "cash" : "bank_transfer",
  status: tx.status === "Completed" ? TransactionStatus.COMPLETED : TransactionStatus.PENDING,
  location: tx.location || "loc1",
  source: TransactionSource.MANUAL,
  createdAt: new Date(),
  updatedAt: new Date()
}));

interface TransactionContextType {
  transactions: Transaction[];
  addTransaction: (transaction: TransactionCreate) => Transaction;
  updateTransaction: (id: string, transaction: Partial<Transaction>) => Transaction | null;
  deleteTransaction: (id: string) => boolean;
  getTransaction: (id: string) => Transaction | null;
  filterTransactions: (filter: TransactionFilter) => Transaction[];
  getTransactionsBySource: (source: TransactionSource) => Transaction[];
  getTransactionsByDateRange: (startDate: Date, endDate: Date) => Transaction[];
  getTransactionsByDate: (date: Date) => Transaction[];
}

const TransactionContext = createContext<TransactionContextType | undefined>(undefined);

export function TransactionProvider({ children }: { children: React.ReactNode }) {
  const [transactions, setTransactions] = useState<Transaction[]>(convertedMockTransactions);

  // Load transactions from localStorage on mount
  useEffect(() => {
    const storedTransactions = localStorage.getItem('vanity_transactions');
    if (storedTransactions) {
      try {
        const parsedTransactions = JSON.parse(storedTransactions);
        setTransactions(parsedTransactions);
      } catch (error) {
        console.error('Failed to parse stored transactions:', error);
      }
    }
  }, []);

  // Save transactions to localStorage when they change
  useEffect(() => {
    localStorage.setItem('vanity_transactions', JSON.stringify(transactions));
  }, [transactions]);

  // Add a new transaction
  const addTransaction = (transaction: TransactionCreate): Transaction => {
    const newTransaction: Transaction = {
      ...transaction,
      id: transaction.id || `TX-${uuidv4().substring(0, 8)}`,
      createdAt: new Date(),
      updatedAt: new Date()
    };

    setTransactions(prev => [...prev, newTransaction]);
    return newTransaction;
  };

  // Update an existing transaction
  const updateTransaction = (id: string, updates: Partial<Transaction>): Transaction | null => {
    let updatedTransaction: Transaction | null = null;

    setTransactions(prev => {
      const index = prev.findIndex(tx => tx.id === id);
      if (index === -1) return prev;

      const updated = {
        ...prev[index],
        ...updates,
        updatedAt: new Date()
      };
      
      updatedTransaction = updated;
      const newTransactions = [...prev];
      newTransactions[index] = updated;
      return newTransactions;
    });

    return updatedTransaction;
  };

  // Delete a transaction
  const deleteTransaction = (id: string): boolean => {
    let deleted = false;

    setTransactions(prev => {
      const index = prev.findIndex(tx => tx.id === id);
      if (index === -1) return prev;

      deleted = true;
      const newTransactions = [...prev];
      newTransactions.splice(index, 1);
      return newTransactions;
    });

    return deleted;
  };

  // Get a transaction by ID
  const getTransaction = (id: string): Transaction | null => {
    return transactions.find(tx => tx.id === id) || null;
  };

  // Filter transactions based on criteria
  const filterTransactions = (filter: TransactionFilter): Transaction[] => {
    return transactions.filter(tx => {
      // Convert date strings to Date objects if needed
      const txDate = typeof tx.date === 'string' ? new Date(tx.date) : tx.date;
      
      // Filter by date range
      if (filter.startDate && filter.endDate) {
        if (isBefore(txDate, filter.startDate) || isAfter(txDate, filter.endDate)) {
          return false;
        }
      }
      
      // Filter by single date
      if (filter.singleDate && !isSameDay(txDate, filter.singleDate)) {
        return false;
      }
      
      // Filter by type
      if (filter.type && tx.type !== filter.type) {
        return false;
      }
      
      // Filter by source
      if (filter.source && tx.source !== filter.source) {
        return false;
      }
      
      // Filter by status
      if (filter.status && tx.status !== filter.status) {
        return false;
      }
      
      // Filter by location
      if (filter.location && filter.location !== 'all' && tx.location !== filter.location) {
        return false;
      }
      
      // Filter by client ID
      if (filter.clientId && tx.clientId !== filter.clientId) {
        return false;
      }
      
      // Filter by staff ID
      if (filter.staffId && tx.staffId !== filter.staffId) {
        return false;
      }
      
      // Filter by search term
      if (filter.search) {
        const searchLower = filter.search.toLowerCase();
        const matchesId = tx.id.toLowerCase().includes(searchLower);
        const matchesClient = tx.clientName?.toLowerCase().includes(searchLower) || false;
        const matchesStaff = tx.staffName?.toLowerCase().includes(searchLower) || false;
        const matchesDescription = tx.description.toLowerCase().includes(searchLower);
        
        if (!(matchesId || matchesClient || matchesStaff || matchesDescription)) {
          return false;
        }
      }
      
      // Filter by amount range
      if (filter.minAmount !== undefined && tx.amount < filter.minAmount) {
        return false;
      }
      
      if (filter.maxAmount !== undefined && tx.amount > filter.maxAmount) {
        return false;
      }
      
      return true;
    });
  };

  // Get transactions by source
  const getTransactionsBySource = (source: TransactionSource): Transaction[] => {
    return transactions.filter(tx => tx.source === source);
  };

  // Get transactions by date range
  const getTransactionsByDateRange = (startDate: Date, endDate: Date): Transaction[] => {
    return transactions.filter(tx => {
      const txDate = typeof tx.date === 'string' ? new Date(tx.date) : tx.date;
      return !isBefore(txDate, startDate) && !isAfter(txDate, endDate);
    });
  };

  // Get transactions by single date
  const getTransactionsByDate = (date: Date): Transaction[] => {
    return transactions.filter(tx => {
      const txDate = typeof tx.date === 'string' ? new Date(tx.date) : tx.date;
      return isSameDay(txDate, date);
    });
  };

  const value = {
    transactions,
    addTransaction,
    updateTransaction,
    deleteTransaction,
    getTransaction,
    filterTransactions,
    getTransactionsBySource,
    getTransactionsByDateRange,
    getTransactionsByDate
  };

  return (
    <TransactionContext.Provider value={value}>
      {children}
    </TransactionContext.Provider>
  );
}

export function useTransactions() {
  const context = useContext(TransactionContext);
  if (context === undefined) {
    throw new Error('useTransactions must be used within a TransactionProvider');
  }
  return context;
}
