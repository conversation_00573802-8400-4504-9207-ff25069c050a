import { Location } from "./types/location"

export const locations: Location[] = [
  {
    id: "loc1",
    name: "D-Ring Road",
    address: "123 D-Ring Road",
    city: "Doha",
    state: "Doha",
    country: "Qatar",
    postalCode: "12345",
    timezone: "Asia/Qatar",
    phone: "(*************",
    email: "<EMAIL>",
    isPrimary: true
  },
  {
    id: "loc2",
    name: "<PERSON><PERSON><PERSON>",
    address: "456 Muaither St",
    city: "Doha",
    state: "Doha",
    country: "Qatar",
    postalCode: "23456",
    timezone: "Asia/Qatar",
    phone: "(*************",
    email: "<EMAIL>",
    isPrimary: false
  },
  {
    id: "loc3",
    name: "Medinat Khalifa",
    address: "789 Medinat Khalifa Blvd",
    city: "Doha",
    state: "Doha",
    country: "Qatar",
    postalCode: "34567",
    timezone: "Asia/Qatar",
    phone: "(*************",
    email: "<EMAIL>",
    isPrimary: false
  },
  {
    id: "home",
    name: "Home Service",
    address: "Client's Location",
    city: "Doha",
    state: "Doha",
    country: "Qatar",
    postalCode: "",
    timezone: "Asia/Qatar",
    phone: "(*************",
    email: "<EMAIL>",
    isPrimary: false
  }
]