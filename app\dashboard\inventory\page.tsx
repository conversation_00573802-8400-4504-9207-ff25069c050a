"use client"

import { useState } from "react"
import { useAuth } from "@/lib/auth-provider"
import { useProducts } from "@/lib/product-provider"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { Ta<PERSON>, <PERSON><PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Switch } from "@/components/ui/switch"
import { NewProductDialog } from "@/components/inventory/new-product-dialog"
import { StockAdjustmentDialog } from "@/components/inventory/stock-adjustment-dialog"
import { ProductEditDialog } from "@/components/inventory/product-edit-dialog"
import { CategoryManagementDialog } from "@/components/inventory/category-management-dialog"
import { ProductTransferDialog } from "@/components/inventory/product-transfer-dialog"
import { AccessDenied } from "@/components/access-denied"
import { AlertCircle, Plus, Search, Eye, EyeOff, Edit, Star, ShoppingCart, Image as ImageIcon, Settings, ArrowRightLeft } from "lucide-react"
import { CurrencyDisplay } from "@/components/ui/currency-display"
import { useCurrency } from "@/lib/currency-provider"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"

// Mock data - would be replaced with actual API calls
const mockProducts = [
  {
    id: "1",
    name: "Hydrating Shampoo",
    sku: "SH-001",
    category: "Hair Care",
    price: 24.99,
    cost: 12.5,
    stock: 32,
    minStock: 10,
    type: "retail",
    location: "loc1",
    // E-commerce features
    isActive: true,
    isFeatured: false,
    isNew: true,
    isBestSeller: false,
    images: ["https://images.unsplash.com/photo-1608248597279-f99d160bfcbc?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80"],
    description: "Our Hydrating Shampoo is specially formulated to nourish and revitalize dry, damaged hair.",
    rating: 4.8,
    reviewCount: 124,
  },
  {
    id: "2",
    name: "Volumizing Conditioner",
    sku: "CO-001",
    category: "Hair Care",
    price: 22.99,
    cost: 11.25,
    stock: 28,
    minStock: 10,
    type: "retail",
    location: "loc1",
    // E-commerce features
    isActive: true,
    isFeatured: true,
    isNew: false,
    isBestSeller: true,
    images: ["https://images.unsplash.com/photo-1631729371254-42c2892f0e6e?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80"],
    description: "Give your hair the volume it deserves with our Volumizing Conditioner.",
    rating: 4.7,
    reviewCount: 98,
  },
  {
    id: "3",
    name: "Hair Color - Blonde",
    sku: "HC-001",
    category: "Color",
    price: 0,
    cost: 15.75,
    stock: 15,
    minStock: 5,
    type: "professional",
    location: "loc1",
  },
  {
    id: "4",
    name: "Hair Color - Brown",
    sku: "HC-002",
    category: "Color",
    price: 0,
    cost: 15.75,
    stock: 3,
    minStock: 5,
    type: "professional",
    location: "loc1",
  },
  {
    id: "5",
    name: "Styling Gel",
    sku: "SG-001",
    category: "Styling",
    price: 18.99,
    cost: 9.5,
    stock: 42,
    minStock: 15,
    type: "retail",
    location: "loc1",
    // E-commerce features
    isActive: true,
    isFeatured: false,
    isNew: false,
    isBestSeller: false,
    images: ["https://images.unsplash.com/photo-1626784215021-2e39ccf971cd?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80"],
    description: "Create voluminous, bouncy styles with our lightweight Styling Gel.",
    rating: 4.5,
    reviewCount: 67,
  },
  {
    id: "6",
    name: "Hair Spray",
    sku: "HS-001",
    category: "Styling",
    price: 16.99,
    cost: 8.25,
    stock: 38,
    minStock: 15,
    type: "retail",
    location: "loc1",
    // E-commerce features
    isActive: true,
    isFeatured: false,
    isNew: false,
    isBestSeller: true,
    images: ["https://images.unsplash.com/photo-1610705267928-1b9f2fa7f1c5?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80"],
    description: "Professional-grade hair spray for long-lasting hold.",
    rating: 4.6,
    reviewCount: 89,
  },
  {
    id: "7",
    name: "Repair & Restore Hair Mask",
    sku: "TM-001",
    category: "Treatments",
    price: 32.99,
    salePrice: 27.99,
    cost: 16.5,
    stock: 12,
    minStock: 8,
    type: "retail",
    location: "loc1",
    // E-commerce features
    isActive: true,
    isFeatured: true,
    isNew: false,
    isBestSeller: true,
    isOnSale: true,
    images: ["https://images.unsplash.com/photo-1620916566398-39f1143ab7be?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80"],
    description: "Transform damaged hair with our intensive Repair & Restore Hair Mask.",
    rating: 4.9,
    reviewCount: 156,
  },
  {
    id: "8",
    name: "Bleach Powder",
    sku: "BP-001",
    category: "Color",
    price: 0,
    cost: 22.0,
    stock: 7,
    minStock: 10,
    type: "professional",
    location: "loc1",
  },
]

export default function InventoryPage() {
  const { currentLocation, hasPermission } = useAuth()
  const { formatCurrency } = useCurrency()
  const { products, getRetailProducts } = useProducts()
  const [search, setSearch] = useState("")
  const [activeTab, setActiveTab] = useState("all")
  const [isNewProductDialogOpen, setIsNewProductDialogOpen] = useState(false)
  const [isStockAdjustmentDialogOpen, setIsStockAdjustmentDialogOpen] = useState(false)
  const [isProductEditDialogOpen, setIsProductEditDialogOpen] = useState(false)
  const [isCategoryManagementDialogOpen, setIsCategoryManagementDialogOpen] = useState(false)
  const [isProductTransferDialogOpen, setIsProductTransferDialogOpen] = useState(false)
  const [selectedProduct, setSelectedProduct] = useState<any>(null)

  // Check if user has permission to view inventory page
  if (!hasPermission("view_inventory")) {
    return (
      <AccessDenied
        description="You don't have permission to view the inventory management page."
        backButtonHref="/dashboard"
      />
    )
  }

  // Filter products based on location, search term, and active tab
  const filteredProducts = products.filter((product) => {
    // Filter by location
    if (product.location !== currentLocation && currentLocation !== "all") {
      return false
    }

    // Filter by search term
    if (
      search &&
      !product.name.toLowerCase().includes(search.toLowerCase()) &&
      !product.sku.toLowerCase().includes(search.toLowerCase())
    ) {
      return false
    }

    // Filter by tab
    if (activeTab === "retail" && !product.isRetail) {
      return false
    }

    if (activeTab === "professional" && product.isRetail) {
      return false
    }

    if (activeTab === "low-stock" && product.stock >= product.minStock) {
      return false
    }

    return true
  })

  const handleAdjustStock = (product: any) => {
    setSelectedProduct(product)
    setIsStockAdjustmentDialogOpen(true)
  }

  const handleEditProduct = (product: any) => {
    setSelectedProduct(product)
    setIsProductEditDialogOpen(true)
  }

  const handleTransferProduct = (product: any) => {
    setSelectedProduct(product)
    setIsProductTransferDialogOpen(true)
  }

  const lowStockCount = products.filter(
    (p) => (p.location === currentLocation || currentLocation === "all") && p.stock < p.minStock,
  ).length

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">Inventory Management</h2>
          <p className="text-muted-foreground">
            {currentLocation === "all"
              ? "Manage inventory across all locations"
              : `Manage inventory at ${currentLocation === "loc1" ? "Downtown" : currentLocation === "loc2" ? "Westside" : "Northside"} location`}
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            onClick={() => setIsCategoryManagementDialogOpen(true)}
          >
            <Settings className="mr-2 h-4 w-4" />
            Manage Categories
          </Button>
          {hasPermission("create_inventory") && (
            <Button onClick={() => setIsNewProductDialogOpen(true)}>
              <Plus className="mr-2 h-4 w-4" />
              Add Product
            </Button>
          )}
        </div>
      </div>

      {lowStockCount > 0 && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Low Stock Alert</AlertTitle>
          <AlertDescription>
            {lowStockCount} product{lowStockCount > 1 ? "s" : ""} {lowStockCount > 1 ? "are" : "is"} below the minimum
            stock level.
          </AlertDescription>
        </Alert>
      )}

      <Card>
        <CardHeader className="space-y-0 pb-2">
          <div className="flex flex-col sm:flex-row justify-between gap-4">
            <CardTitle>Product Inventory</CardTitle>
            <div className="relative w-full sm:w-64">
              <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search products..."
                value={search}
                onChange={(e) => setSearch(e.target.value)}
                className="pl-8"
              />
            </div>
          </div>
          <CardDescription>Manage your salon's retail and professional products. Retail products automatically appear in the client shop.</CardDescription>
        </CardHeader>
        <CardContent>
          <Tabs defaultValue="all" value={activeTab} onValueChange={setActiveTab} className="w-full">
            <TabsList className="mb-4">
              <TabsTrigger value="all">All Products</TabsTrigger>
              <TabsTrigger value="retail">Retail & Shop</TabsTrigger>
              <TabsTrigger value="professional">Professional Use</TabsTrigger>
              <TabsTrigger value="low-stock" className="relative">
                Low Stock
                {lowStockCount > 0 && (
                  <Badge variant="destructive" className="ml-2 px-1.5 py-0.5 h-5 min-w-5 text-xs">
                    {lowStockCount}
                  </Badge>
                )}
              </TabsTrigger>
            </TabsList>
            <TabsContent value="all" className="m-0">
              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Product Name</TableHead>
                      <TableHead>SKU</TableHead>
                      <TableHead>Category</TableHead>
                      <TableHead className="text-right">Price</TableHead>
                      <TableHead className="text-right">Cost</TableHead>
                      <TableHead className="text-center">Stock</TableHead>
                      <TableHead>Type</TableHead>
                      <TableHead className="text-right">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredProducts.length === 0 ? (
                      <TableRow>
                        <TableCell colSpan={8} className="h-24 text-center">
                          No products found.
                        </TableCell>
                      </TableRow>
                    ) : (
                      filteredProducts.map((product) => (
                        <TableRow key={product.id}>
                          <TableCell className="font-medium">{product.name}</TableCell>
                          <TableCell>{product.sku}</TableCell>
                          <TableCell>{product.category}</TableCell>
                          <TableCell className="text-right">
                            {product.price > 0 ? <CurrencyDisplay amount={product.price} /> : "-"}
                          </TableCell>
                          <TableCell className="text-right"><CurrencyDisplay amount={product.cost} /></TableCell>
                          <TableCell className="text-center">
                            <Badge
                              variant={product.stock < product.minStock ? "destructive" : "outline"}
                              className="w-16"
                            >
                              {product.stock}
                            </Badge>
                          </TableCell>
                          <TableCell>
                            <Badge variant={product.isRetail ? "default" : "secondary"}>
                              {product.isRetail ? "Retail" : "Professional"}
                            </Badge>
                          </TableCell>
                          <TableCell className="text-right">
                            <div className="flex gap-1 justify-end">
                              <Button variant="ghost" size="sm" onClick={() => handleEditProduct(product)}>
                                <Edit className="h-4 w-4" />
                              </Button>
                              <Button variant="ghost" size="sm" onClick={() => handleAdjustStock(product)}>
                                Adjust
                              </Button>
                              <Button variant="ghost" size="sm" onClick={() => handleTransferProduct(product)}>
                                <ArrowRightLeft className="h-4 w-4" />
                              </Button>
                            </div>
                          </TableCell>
                        </TableRow>
                      ))
                    )}
                  </TableBody>
                </Table>
              </div>
            </TabsContent>
            <TabsContent value="retail" className="m-0">
              <div className="mb-4 p-4 bg-blue-50 rounded-lg border border-blue-200">
                <div className="flex items-center gap-2 mb-2">
                  <ShoppingCart className="h-5 w-5 text-blue-600" />
                  <h3 className="font-medium text-blue-900">E-commerce & Client Shop Management</h3>
                </div>
                <p className="text-sm text-blue-700">
                  Manage retail products that appear in your client portal shop. Control visibility, pricing, and product features.
                </p>
              </div>

              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead className="w-12"></TableHead>
                      <TableHead>Product</TableHead>
                      <TableHead>SKU</TableHead>
                      <TableHead>Category</TableHead>
                      <TableHead className="text-right">Price</TableHead>
                      <TableHead className="text-center">Stock</TableHead>
                      <TableHead className="text-center">Status</TableHead>
                      <TableHead className="text-center">Features</TableHead>
                      <TableHead className="text-right">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredProducts.length === 0 ? (
                      <TableRow>
                        <TableCell colSpan={9} className="h-24 text-center">
                          No retail products found.
                        </TableCell>
                      </TableRow>
                    ) : (
                      filteredProducts.map((product) => (
                        <TableRow key={product.id}>
                          <TableCell>
                            {product.images && product.images[0] ? (
                              <img
                                src={product.images[0]}
                                alt={product.name}
                                className="w-10 h-10 object-cover rounded border"
                              />
                            ) : (
                              <div className="w-10 h-10 bg-gray-100 rounded border flex items-center justify-center">
                                <ImageIcon className="h-4 w-4 text-gray-400" />
                              </div>
                            )}
                          </TableCell>
                          <TableCell>
                            <div>
                              <div className="font-medium">{product.name}</div>
                              {product.rating && (
                                <div className="flex items-center gap-1 text-sm text-gray-500">
                                  <Star className="h-3 w-3 fill-yellow-400 text-yellow-400" />
                                  {product.rating} ({product.reviewCount} reviews)
                                </div>
                              )}
                            </div>
                          </TableCell>
                          <TableCell>{product.sku}</TableCell>
                          <TableCell>{product.category}</TableCell>
                          <TableCell className="text-right">
                            <div>
                              {product.salePrice ? (
                                <div>
                                  <div className="line-through text-gray-500 text-sm">
                                    <CurrencyDisplay amount={product.price} />
                                  </div>
                                  <div className="text-red-600 font-medium">
                                    <CurrencyDisplay amount={product.salePrice} />
                                  </div>
                                </div>
                              ) : (
                                <CurrencyDisplay amount={product.price} />
                              )}
                            </div>
                          </TableCell>
                          <TableCell className="text-center">
                            <Badge
                              variant={product.stock < product.minStock ? "destructive" : "outline"}
                              className="w-16"
                            >
                              {product.stock}
                            </Badge>
                          </TableCell>
                          <TableCell className="text-center">
                            <div className="flex items-center justify-center">
                              {product.isActive ? (
                                <Eye className="h-4 w-4 text-green-600" title="Visible in shop" />
                              ) : (
                                <EyeOff className="h-4 w-4 text-gray-400" title="Hidden from shop" />
                              )}
                            </div>
                          </TableCell>
                          <TableCell className="text-center">
                            <div className="flex flex-wrap gap-1 justify-center">
                              {product.isFeatured && (
                                <Badge variant="default" className="text-xs">Featured</Badge>
                              )}
                              {product.isNew && (
                                <Badge variant="secondary" className="text-xs">New</Badge>
                              )}
                              {product.isBestSeller && (
                                <Badge variant="outline" className="text-xs">Best Seller</Badge>
                              )}
                              {product.isOnSale && (
                                <Badge variant="destructive" className="text-xs">Sale</Badge>
                              )}
                            </div>
                          </TableCell>
                          <TableCell className="text-right">
                            <div className="flex gap-1 justify-end">
                              <Button variant="ghost" size="sm" onClick={() => handleEditProduct(product)} title="Edit product">
                                <Edit className="h-4 w-4" />
                              </Button>
                              <Button variant="ghost" size="sm" onClick={() => handleAdjustStock(product)} title="Adjust stock">
                                Stock
                              </Button>
                            </div>
                          </TableCell>
                        </TableRow>
                      ))
                    )}
                  </TableBody>
                </Table>
              </div>
            </TabsContent>
            <TabsContent value="professional" className="m-0">
              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Product Name</TableHead>
                      <TableHead>SKU</TableHead>
                      <TableHead>Category</TableHead>
                      <TableHead className="text-right">Cost</TableHead>
                      <TableHead className="text-center">Stock</TableHead>
                      <TableHead className="text-right">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredProducts.length === 0 ? (
                      <TableRow>
                        <TableCell colSpan={6} className="h-24 text-center">
                          No professional products found.
                        </TableCell>
                      </TableRow>
                    ) : (
                      filteredProducts.map((product) => (
                        <TableRow key={product.id}>
                          <TableCell className="font-medium">{product.name}</TableCell>
                          <TableCell>{product.sku}</TableCell>
                          <TableCell>{product.category}</TableCell>
                          <TableCell className="text-right"><CurrencyDisplay amount={product.cost} /></TableCell>
                          <TableCell className="text-center">
                            <Badge
                              variant={product.stock < product.minStock ? "destructive" : "outline"}
                              className="w-16"
                            >
                              {product.stock}
                            </Badge>
                          </TableCell>
                          <TableCell className="text-right">
                            <Button variant="ghost" size="sm" onClick={() => handleAdjustStock(product)}>
                              Adjust
                            </Button>
                          </TableCell>
                        </TableRow>
                      ))
                    )}
                  </TableBody>
                </Table>
              </div>
            </TabsContent>
            <TabsContent value="low-stock" className="m-0">
              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Product Name</TableHead>
                      <TableHead>SKU</TableHead>
                      <TableHead>Category</TableHead>
                      <TableHead className="text-center">Current Stock</TableHead>
                      <TableHead className="text-center">Min Stock</TableHead>
                      <TableHead>Type</TableHead>
                      <TableHead className="text-right">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredProducts.length === 0 ? (
                      <TableRow>
                        <TableCell colSpan={7} className="h-24 text-center">
                          No low stock products found.
                        </TableCell>
                      </TableRow>
                    ) : (
                      filteredProducts.map((product) => (
                        <TableRow key={product.id}>
                          <TableCell className="font-medium">{product.name}</TableCell>
                          <TableCell>{product.sku}</TableCell>
                          <TableCell>{product.category}</TableCell>
                          <TableCell className="text-center">
                            <Badge variant="destructive" className="w-16">
                              {product.stock}
                            </Badge>
                          </TableCell>
                          <TableCell className="text-center">{product.minStock}</TableCell>
                          <TableCell>
                            <Badge variant={product.isRetail ? "default" : "secondary"}>
                              {product.isRetail ? "Retail" : "Professional"}
                            </Badge>
                          </TableCell>
                          <TableCell className="text-right">
                            <div className="flex gap-1 justify-end">
                              <Button variant="ghost" size="sm" onClick={() => handleEditProduct(product)}>
                                <Edit className="h-4 w-4" />
                              </Button>
                              <Button variant="ghost" size="sm" onClick={() => handleAdjustStock(product)}>
                                Adjust
                              </Button>
                              <Button variant="ghost" size="sm" onClick={() => handleTransferProduct(product)}>
                                <ArrowRightLeft className="h-4 w-4" />
                              </Button>
                            </div>
                          </TableCell>
                        </TableRow>
                      ))
                    )}
                  </TableBody>
                </Table>
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>

      <NewProductDialog open={isNewProductDialogOpen} onOpenChange={setIsNewProductDialogOpen} />

      <ProductEditDialog
        open={isProductEditDialogOpen}
        onOpenChange={setIsProductEditDialogOpen}
        product={selectedProduct}
      />

      <StockAdjustmentDialog
        open={isStockAdjustmentDialogOpen}
        onOpenChange={setIsStockAdjustmentDialogOpen}
        product={selectedProduct}
      />

      <CategoryManagementDialog
        open={isCategoryManagementDialogOpen}
        onOpenChange={setIsCategoryManagementDialogOpen}
      />

      <ProductTransferDialog
        open={isProductTransferDialogOpen}
        onOpenChange={setIsProductTransferDialogOpen}
        product={selectedProduct}
      />

      <ProductEditDialog
        open={isProductEditDialogOpen}
        onOpenChange={setIsProductEditDialogOpen}
        product={selectedProduct}
      />
    </div>
  )
}

