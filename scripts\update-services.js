"use strict";

// This script updates the services in localStorage to remove the unknown location "loc4"
// and replace it with "home" for Henna and Weyba Tis services

// Function to update services in localStorage
function updateServices() {
  try {
    // Get services from localStorage
    const servicesJson = localStorage.getItem('vanity_services');
    
    if (!servicesJson) {
      console.log('No services found in localStorage. The mock data will be used on next app load.');
      return;
    }
    
    // Parse services
    let services = JSON.parse(servicesJson);
    
    if (!Array.isArray(services)) {
      console.error('Services in localStorage is not an array');
      return;
    }
    
    // Update Henna and Weyba Tis services
    const updatedServices = services.map(service => {
      // Check if this is a Henna or Weyba Tis service
      if (service.category === 'Henna' || service.category === 'Weyba Tis') {
        // Replace loc4 with home in locations array
        if (Array.isArray(service.locations)) {
          service.locations = service.locations.map(loc => loc === 'loc4' ? 'home' : loc);
        }
      }
      return service;
    });
    
    // Save updated services back to localStorage
    localStorage.setItem('vanity_services', JSON.stringify(updatedServices));
    
    console.log('Services updated successfully. Henna and Weyba Tis services now use "home" instead of "loc4".');
  } catch (error) {
    console.error('Error updating services:', error);
  }
}

// Execute the update function
updateServices();

console.log('Script completed. Please refresh the page to see the changes.');
