"use client"

import type React from "react"

import { useState } from "react"
import { useAuth } from "@/lib/auth-provider"
import { Button } from "@/components/ui/button"
import { Calendar } from "@/components/ui/calendar"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Label } from "@/components/ui/label"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { format } from "date-fns"
import { CalendarIcon } from "lucide-react"
import { mockClients, mockServices, mockStaff } from "@/lib/mock-data"

interface NewAppointmentDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
}

export function NewAppointmentDialog({ open, onOpenChange }: NewAppointmentDialogProps) {
  const { currentLocation } = useAuth()
  const [date, setDate] = useState<Date | undefined>(new Date())
  const [time, setTime] = useState("10:00")
  const [clientId, setClientId] = useState("")
  const [serviceId, setServiceId] = useState("")
  const [staffId, setStaffId] = useState("")

  // Filter clients, services, and staff based on location
  const filteredClients = mockClients.filter(
    (client) => currentLocation === "all" || client.locations.includes(currentLocation),
  )

  const filteredServices = mockServices.filter(
    (service) => currentLocation === "all" || service.locations.includes(currentLocation),
  )

  const filteredStaff = mockStaff.filter(
    (staff) => {
      // For regular locations, check if staff is assigned to that location
      if (currentLocation !== "all" && currentLocation !== "home") {
        return staff.locations.includes(currentLocation);
      }
      // For Home service location, include staff with homeService flag OR staff with "home" in their locations
      else if (currentLocation === "home") {
        return staff.homeService === true || staff.locations.includes("home");
      }
      // For "all" locations, include all staff
      return true;
    }
  )

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    // In a real app, this would create a new appointment via API
    const selectedDateTime = new Date(date!)
    const [hours, minutes] = time.split(':')
    selectedDateTime.setHours(parseInt(hours), parseInt(minutes))

    const selectedService = mockServices.find(service => service.id === serviceId)
    const selectedClient = mockClients.find(client => client.id === clientId)
    const selectedStaff = mockStaff.find(staff => staff.id === staffId)

    if (!selectedService || !selectedClient || !selectedStaff) return

    const newAppointment = {
      id: Math.random().toString(36).substring(7),
      clientId,
      clientName: selectedClient.name,
      date: selectedDateTime.toISOString(),
      service: selectedService.name,
      duration: selectedService.duration,
      staffId,
      staffName: selectedStaff.name,
      location: currentLocation,
      status: 'pending' as const,
      statusHistory: [
        {
          status: 'pending' as const,
          timestamp: new Date().toISOString(),
          updatedBy: 'system'
        }
      ],
      price: selectedService.price
    }

    console.log(newAppointment)
    onOpenChange(false)
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <form onSubmit={handleSubmit}>
          <DialogHeader>
            <DialogTitle>New Appointment</DialogTitle>
            <DialogDescription>Create a new appointment for a client.</DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="client" className="text-right">
                Client
              </Label>
              <Select value={clientId} onValueChange={setClientId} required>
                <SelectTrigger id="client" className="col-span-3">
                  <SelectValue placeholder="Select client" />
                </SelectTrigger>
                <SelectContent>
                  {filteredClients.map((client) => (
                    <SelectItem key={client.id} value={client.id}>
                      {client.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="service" className="text-right">
                Service
              </Label>
              <Select value={serviceId} onValueChange={setServiceId} required>
                <SelectTrigger id="service" className="col-span-3">
                  <SelectValue placeholder="Select service" />
                </SelectTrigger>
                <SelectContent>
                  {filteredServices.map((service) => (
                    <SelectItem key={service.id} value={service.id}>
                      {service.name} ({service.duration} min)
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="staff" className="text-right">
                Staff
              </Label>
              <Select value={staffId} onValueChange={setStaffId} required>
                <SelectTrigger id="staff" className="col-span-3">
                  <SelectValue placeholder="Select staff" />
                </SelectTrigger>
                <SelectContent>
                  {filteredStaff.map((staff) => (
                    <SelectItem key={staff.id} value={staff.id}>
                      {staff.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label className="text-right">Date</Label>
              <Popover>
                <PopoverTrigger asChild>
                  <Button variant="outline" className="col-span-3 justify-start text-left font-normal">
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {date ? format(date, "PPP") : <span>Pick a date</span>}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0">
                  <Calendar mode="single" selected={date} onSelect={setDate} initialFocus />
                </PopoverContent>
              </Popover>
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="time" className="text-right">
                Time
              </Label>
              <div className="col-span-3">
                <Select value={time} onValueChange={setTime} required>
                  <SelectTrigger id="time">
                    <SelectValue placeholder="Select time" />
                  </SelectTrigger>
                  <SelectContent>
                    {Array.from({ length: 24 }, (_, i) => {
                      const hour = i + 9
                      if (hour >= 9 && hour <= 19) {
                        return [
                          <SelectItem key={`${hour}:00`} value={`${hour}:00`}>
                            {hour > 12 ? hour - 12 : hour}:00 {hour >= 12 ? "PM" : "AM"}
                          </SelectItem>,
                          <SelectItem key={`${hour}:30`} value={`${hour}:30`}>
                            {hour > 12 ? hour - 12 : hour}:30 {hour >= 12 ? "PM" : "AM"}
                          </SelectItem>,
                        ]
                      }
                      return null
                    })}
                  </SelectContent>
                </Select>
              </div>
            </div>
          </div>
          <DialogFooter>
            <Button type="submit">Create Appointment</Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
}

