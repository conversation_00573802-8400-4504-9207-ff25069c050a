# Vanity Hub - Next.js 15 Enhanced

Vanity Hub is a modern salon management application built with Next.js 15, featuring enhanced performance and data handling capabilities.

## Next.js 15 Enhancements

This project has been enhanced with Next.js 15 features to improve performance and user experience:

- **Improved Client-Side Router Caching**: Using `staleTimes` configuration for better caching
- **Enhanced Data Fetching**: Using React's `cache` and Next.js's `revalidateTag` for optimized data fetching
- **Currency-Aware Revalidation**: Automatic data revalidation when currency changes
- **Optimized Package Imports**: Expanded list of optimized package imports for better performance

## Demo

Visit the [Enhanced Demo Page](/enhanced-demo) to see the Next.js 15 enhancements in action.

## Getting Started

1. Clone the repository
2. Install dependencies:
   ```bash
   pnpm install
   ```
3. Run the development server:
   ```bash
   pnpm dev
   ```
4. Open [http://localhost:3000](http://localhost:3000) in your browser

## Key Features

- **Multi-Location Scheduling**: Manage appointments across multiple salon locations
- **Client Management**: Keep track of client preferences and appointment history
- **Inventory & POS**: Track retail products and process sales
- **Advanced Analytics**: Gain insights into your business with comprehensive reports
- **Secure Access Control**: Protect your data with role-based access control
- **Online Booking**: Allow clients to book appointments online 24/7

## Documentation

For detailed documentation on the Next.js 15 enhancements, see [NEXT15_ENHANCEMENTS.md](docs/NEXT15_ENHANCEMENTS.md).

## Technologies Used

- **Next.js 15**: React framework with improved performance features
- **React 19**: Latest version of React with improved rendering capabilities
- **TypeScript**: Type-safe JavaScript
- **Tailwind CSS**: Utility-first CSS framework
- **shadcn/ui**: Reusable UI components
- **ESLint**: Code linting with flat config format

## License

This project is licensed under the MIT License - see the LICENSE file for details.
