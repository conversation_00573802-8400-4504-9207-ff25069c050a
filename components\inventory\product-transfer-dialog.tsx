"use client"

import React, { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { useToast } from "@/components/ui/use-toast"
import { useProducts } from "@/lib/product-provider"
import { useAuth } from "@/lib/auth-provider"
import { useLocations } from "@/lib/location-provider"
import { Product } from "@/lib/products-data"
import { ArrowRight, Package, MapPin } from "lucide-react"

interface ProductTransferDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  product?: Product
}

export function ProductTransferDialog({ open, onOpenChange, product }: ProductTransferDialogProps) {
  const { toast } = useToast()
  const { createTransfer } = useProducts()
  const { currentUser, currentLocation } = useAuth()
  const { locations, getLocationById } = useLocations()
  const [isSubmitting, setIsSubmitting] = useState(false)

  const [formData, setFormData] = useState({
    fromLocationId: currentLocation || (locations.length > 0 ? locations[0].id : ""),
    toLocationId: "",
    quantity: 1,
    notes: "",
  })

  const handleChange = (field: string, value: string | number) => {
    setFormData((prev) => ({ ...prev, [field]: value }))
  }

  const resetForm = () => {
    setFormData({
      fromLocationId: currentLocation || (locations.length > 0 ? locations[0].id : ""),
      toLocationId: "",
      quantity: 1,
      notes: "",
    })
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!product) return

    setIsSubmitting(true)

    try {
      // Validate transfer
      if (formData.fromLocationId === formData.toLocationId) {
        toast({
          variant: "destructive",
          title: "Invalid transfer",
          description: "Source and destination locations cannot be the same.",
        })
        return
      }

      if (formData.quantity <= 0) {
        toast({
          variant: "destructive",
          title: "Invalid quantity",
          description: "Transfer quantity must be greater than 0.",
        })
        return
      }

      // In a real app, we would check available stock at the source location
      if (formData.quantity > product.stock) {
        toast({
          variant: "destructive",
          title: "Insufficient stock",
          description: `Only ${product.stock} units available at source location.`,
        })
        return
      }

      // Create transfer
      const transfer = createTransfer({
        productId: product.id,
        productName: product.name,
        fromLocationId: formData.fromLocationId,
        toLocationId: formData.toLocationId,
        quantity: formData.quantity,
        status: 'pending',
        notes: formData.notes,
        createdBy: currentUser?.id || "system",
      })

      toast({
        title: "Transfer created",
        description: `Transfer request for ${formData.quantity} units of ${product.name} has been created.`,
      })

      onOpenChange(false)
      resetForm()
    } catch (error) {
      console.error("Failed to create transfer:", error)
      toast({
        variant: "destructive",
        title: "Error",
        description: "Failed to create transfer. Please try again.",
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  const fromLocation = getLocationById(formData.fromLocationId)
  const toLocation = getLocationById(formData.toLocationId)

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle>Transfer Product</DialogTitle>
          <DialogDescription>
            Move inventory between salon locations. This will create a transfer request that needs to be approved.
          </DialogDescription>
        </DialogHeader>

        {product && (
          <Card className="mb-4">
            <CardHeader className="pb-3">
              <CardTitle className="text-base flex items-center gap-2">
                <Package className="h-4 w-4" />
                Product Details
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              <div className="flex justify-between">
                <span className="font-medium">{product.name}</span>
                <Badge variant="outline">{product.sku}</Badge>
              </div>
              <div className="flex justify-between text-sm text-muted-foreground">
                <span>Category: {product.category}</span>
                <span>Current Stock: {product.stock} units</span>
              </div>
            </CardContent>
          </Card>
        )}

        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="fromLocation">From Location *</Label>
              <Select
                value={formData.fromLocationId}
                onValueChange={(value) => handleChange("fromLocationId", value)}
              >
                <SelectTrigger id="fromLocation">
                  <SelectValue placeholder="Select source location" />
                </SelectTrigger>
                <SelectContent>
                  {locations.map((location) => (
                    <SelectItem key={location.id} value={location.id}>
                      {location.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="toLocation">To Location *</Label>
              <Select
                value={formData.toLocationId}
                onValueChange={(value) => handleChange("toLocationId", value)}
              >
                <SelectTrigger id="toLocation">
                  <SelectValue placeholder="Select destination location" />
                </SelectTrigger>
                <SelectContent>
                  {locations
                    .filter(loc => loc.id !== formData.fromLocationId)
                    .map((location) => (
                      <SelectItem key={location.id} value={location.id}>
                        {location.name}
                      </SelectItem>
                    ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          {fromLocation && toLocation && (
            <Card className="bg-blue-50 border-blue-200">
              <CardContent className="p-4">
                <div className="flex items-center justify-between text-sm">
                  <div className="flex items-center gap-2">
                    <MapPin className="h-4 w-4 text-blue-600" />
                    <div>
                      <div className="font-medium">{fromLocation.name}</div>
                      <div className="text-muted-foreground">{fromLocation.address}</div>
                    </div>
                  </div>
                  <ArrowRight className="h-4 w-4 text-blue-600" />
                  <div className="flex items-center gap-2">
                    <div className="text-right">
                      <div className="font-medium">{toLocation.name}</div>
                      <div className="text-muted-foreground">{toLocation.address}</div>
                    </div>
                    <MapPin className="h-4 w-4 text-blue-600" />
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          <div className="space-y-2">
            <Label htmlFor="quantity">Quantity to Transfer *</Label>
            <Input
              id="quantity"
              type="number"
              min="1"
              max={product?.stock || 999}
              value={formData.quantity}
              onChange={(e) => handleChange("quantity", parseInt(e.target.value) || 1)}
              required
              placeholder="Enter quantity"
            />
            {product && (
              <p className="text-sm text-muted-foreground">
                Maximum available: {product.stock} units
              </p>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="notes">Notes (Optional)</Label>
            <Textarea
              id="notes"
              value={formData.notes}
              onChange={(e) => handleChange("notes", e.target.value)}
              rows={3}
              placeholder="Add any notes about this transfer..."
            />
          </div>

          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={() => onOpenChange(false)}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={isSubmitting || !formData.toLocationId}
            >
              {isSubmitting ? "Creating Transfer..." : "Create Transfer"}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
}
