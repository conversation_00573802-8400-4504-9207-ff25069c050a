"use client"

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON>eader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Progress } from "@/components/ui/progress"
import { ShoppingCart } from "lucide-react"

export function InventoryAlerts() {
  const inventoryItems = [
    {
      id: 1,
      name: "Shampoo - Professional",
      category: "Hair Care",
      stock: 2,
      minStock: 10,
      maxStock: 50,
      status: "Critical",
    },
    {
      id: 2,
      name: "Conditioner - Professional",
      category: "Hair Care",
      stock: 5,
      minStock: 10,
      maxStock: 50,
      status: "Low",
    },
    {
      id: 3,
      name: "Hair Color - Blonde",
      category: "Color",
      stock: 3,
      minStock: 8,
      maxStock: 30,
      status: "Low",
    },
    {
      id: 4,
      name: "Styling Gel",
      category: "Styling",
      stock: 4,
      minStock: 8,
      maxStock: 40,
      status: "Low",
    },
    {
      id: 5,
      name: "Nail Polish - Red",
      category: "Nail Care",
      stock: 1,
      minStock: 5,
      maxStock: 20,
      status: "Critical",
    },
  ]

  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-xl font-bold">Inventory Alerts</CardTitle>
        <p className="text-sm text-muted-foreground">Products that need to be restocked soon.</p>
      </CardHeader>
      <CardContent>
        <div className="space-y-6">
          {inventoryItems.map((item) => (
            <div key={item.id} className="space-y-2">
              <div className="flex items-center justify-between">
                <div>
                  <div className="flex items-center gap-2">
                    <h4 className="font-medium">{item.name}</h4>
                    <Badge
                      className={
                        item.status === "Critical" ? "bg-red-100 text-red-800" : "bg-yellow-100 text-yellow-800"
                      }
                    >
                      {item.status}
                    </Badge>
                  </div>
                  <p className="text-sm text-muted-foreground">{item.category}</p>
                </div>
                <Button size="sm" variant="outline" className="gap-1">
                  <ShoppingCart className="h-4 w-4" />
                  Order
                </Button>
              </div>
              <div className="space-y-1">
                <div className="flex justify-between text-sm">
                  <span>Stock Level: {item.stock} units</span>
                  <span>{Math.round((item.stock / item.maxStock) * 100)}%</span>
                </div>
                <Progress value={(item.stock / item.maxStock) * 100} className="h-2" />
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  )
}

