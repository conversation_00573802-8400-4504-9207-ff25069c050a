import { LoyaltyD<PERSON>, Loyalty<PERSON><PERSON>ard, Redeemed<PERSON><PERSON><PERSON>, Referral } from "@/lib/types/loyalty";
import { rewardTemplates } from "@/lib/loyalty-management-data";

// Enhanced Mock loyalty data
export const loyaltyData: Record<string, LoyaltyData> = {
  client123: {
    points: 450,
    pointsToNextReward: 50,
    totalPointsEarned: 750,
    memberSince: "Jan 15, 2025",
    tier: "Gold",
    nextTier: "Platinum",
    pointsToNextTier: 250,
    availableRewards: rewardTemplates,
    redeemedRewards: [],
    referralCode: "JANE2025",
    referrals: [
      {
        id: "ref1",
        referrerId: "client123",
        referrerName: "<PERSON>",
        refereeEmail: "<EMAIL>",
        refereeName: "<PERSON>",
        referralCode: "JANE2025",
        status: "completed",
        createdAt: "2025-02-01T10:00:00Z",
        completedAt: "2025-02-05T14:30:00Z",
        pointsAwarded: 200
      },
      {
        id: "ref2",
        referrerId: "client123",
        referrerName: "<PERSON>",
        refereeEmail: "<EMAIL>",
        referralCode: "JANE2025",
        status: "pending",
        createdAt: "2025-03-10T16:20:00Z",
        pointsAwarded: 0
      }
    ],
    activePromotions: [
      {
        id: "promo1",
        name: "Spring Double Points",
        description: "Earn double points on all services during spring",
        type: "double_points",
        value: 2,
        startDate: "2025-03-01T00:00:00Z",
        endDate: "2025-05-31T23:59:59Z",
        isActive: true,
        conditions: {
          serviceTypes: ["haircut", "color", "styling"]
        }
      }
    ]
  },
  ed1: {
    points: 650,
    pointsToNextReward: 100,
    totalPointsEarned: 1250,
    memberSince: "Dec 10, 2024",
    tier: "Platinum",
    nextTier: "Diamond",
    pointsToNextTier: 500,
    availableRewards: rewardTemplates,
    redeemedRewards: [
      {
        id: "rr1",
        rewardId: "r1",
        name: "$25 off your next service",
        redeemedAt: "2025-01-15T10:30:00",
        usedAt: "2025-01-20T14:45:00",
        pointsCost: 500,
        status: "used",
        expiresAt: "2025-04-15T10:30:00"
      }
    ],
    referralCode: "EMILY2024",
    referrals: [
      {
        id: "ref3",
        referrerId: "ed1",
        referrerName: "Emily Davis",
        refereeEmail: "<EMAIL>",
        refereeName: "James Wilson",
        referralCode: "EMILY2024",
        status: "completed",
        createdAt: "2025-01-10T09:00:00Z",
        completedAt: "2025-01-15T11:20:00Z",
        pointsAwarded: 200
      }
    ],
    activePromotions: []
  }
};

// Mock point history
export const pointHistory: Record<string, {
  id: string;
  date: string;
  description: string;
  points: number;
  type: "earned" | "redeemed";
}[]> = {
  client123: [
    {
      id: "ph1",
      date: "2025-03-15T10:30:00",
      description: "Service: Haircut & Style",
      points: 75,
      type: "earned"
    },
    {
      id: "ph2",
      date: "2025-03-01T14:45:00",
      description: "Product Purchase: Hydrating Shampoo",
      points: 45,
      type: "earned"
    },
    {
      id: "ph3",
      date: "2025-02-15T11:15:00",
      description: "Reward Redemption: 25 off service",
      points: -250,
      type: "redeemed"
    },
    {
      id: "ph4",
      date: "2025-02-10T09:30:00",
      description: "Service: Color & Highlights",
      points: 120,
      type: "earned"
    },
    {
      id: "ph5",
      date: "2025-02-01T16:00:00",
      description: "Birthday Bonus",
      points: 250,
      type: "earned"
    },
    {
      id: "ph6",
      date: "2025-01-15T13:20:00",
      description: "Welcome Bonus",
      points: 100,
      type: "earned"
    }
  ],
  ed1: [
    {
      id: "ph1",
      date: "2025-03-20T11:30:00",
      description: "Service: Color & Highlights",
      points: 120,
      type: "earned"
    },
    {
      id: "ph2",
      date: "2025-03-05T15:45:00",
      description: "Product Purchase: Styling Kit",
      points: 75,
      type: "earned"
    },
    {
      id: "ph3",
      date: "2025-02-20T12:15:00",
      description: "Referral Bonus: James Wilson",
      points: 200,
      type: "earned"
    },
    {
      id: "ph4",
      date: "2025-01-20T14:45:00",
      description: "Reward Redemption: 25 off service",
      points: -500,
      type: "redeemed"
    },
    {
      id: "ph5",
      date: "2025-01-10T10:30:00",
      description: "Service: Haircut & Style",
      points: 75,
      type: "earned"
    },
    {
      id: "ph6",
      date: "2024-12-25T16:00:00",
      description: "Holiday Bonus",
      points: 150,
      type: "earned"
    },
    {
      id: "ph7",
      date: "2024-12-10T13:20:00",
      description: "Welcome Bonus",
      points: 100,
      type: "earned"
    }
  ]
};
