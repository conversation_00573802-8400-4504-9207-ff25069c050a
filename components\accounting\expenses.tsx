"use client"

import { Card } from "@/components/ui/card"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import type { DateRange } from "react-day-picker"
import { Eye, MoreHorizontal } from "lucide-react"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { CurrencyDisplay } from "@/components/ui/currency-display"
import { useCurrency } from "@/lib/currency-provider"

interface ExpensesProps {
  search: string
  dateRange?: DateRange
  selectedLocation?: string
}

export function Expenses({ search, dateRange, selectedLocation = "all" }: ExpensesProps) {
  const { formatCurrency } = useCurrency()
  // Mock expenses data
  const expenses = [
    {
      id: "EXP-001",
      date: "Apr 1, 2025",
      category: "Inventory",
      vendor: "Beauty Supply Co.",
      description: "Hair products restock",
      amount: 850.0,
      paymentMethod: "Credit Card",
      status: "Paid",
      location: "loc1", // Downtown Salon
    },
    {
      id: "EXP-002",
      date: "Apr 1, 2025",
      category: "Utilities",
      vendor: "City Power & Water",
      description: "Monthly utilities",
      amount: 320.0,
      paymentMethod: "Bank Transfer",
      status: "Paid",
      location: "loc2", // Westside Salon
    },
    {
      id: "EXP-003",
      date: "Mar 31, 2025",
      category: "Rent",
      vendor: "Downtown Properties",
      description: "Monthly rent - Downtown location",
      amount: 2200.0,
      paymentMethod: "Bank Transfer",
      status: "Paid",
      location: "loc3", // Northside Salon
    },
    {
      id: "EXP-004",
      date: "Mar 30, 2025",
      category: "Payroll",
      vendor: "Staff Payroll",
      description: "Bi-weekly staff payroll",
      amount: 4800.0,
      paymentMethod: "Bank Transfer",
      status: "Paid",
    },
    {
      id: "EXP-005",
      date: "Mar 28, 2025",
      category: "Marketing",
      vendor: "Social Media Ads",
      description: "Monthly social media advertising",
      amount: 250.0,
      paymentMethod: "Credit Card",
      status: "Paid",
    },
    {
      id: "EXP-006",
      date: "Mar 25, 2025",
      category: "Maintenance",
      vendor: "Clean Team Services",
      description: "Weekly cleaning service",
      amount: 180.0,
      paymentMethod: "Credit Card",
      status: "Paid",
    },
    {
      id: "EXP-007",
      date: "Mar 20, 2025",
      category: "Inventory",
      vendor: "Salon Equipment Inc.",
      description: "New hair dryers (3)",
      amount: 450.0,
      paymentMethod: "Credit Card",
      status: "Paid",
    },
    {
      id: "EXP-008",
      date: "Mar 15, 2025",
      category: "Insurance",
      vendor: "Business Shield Insurance",
      description: "Monthly business insurance",
      amount: 175.0,
      paymentMethod: "Bank Transfer",
      status: "Paid",
    },
    {
      id: "EXP-009",
      date: "Mar 15, 2025",
      category: "Payroll",
      vendor: "Staff Payroll",
      description: "Bi-weekly staff payroll",
      amount: 4750.0,
      paymentMethod: "Bank Transfer",
      status: "Paid",
    },
    {
      id: "EXP-010",
      date: "Mar 10, 2025",
      category: "Software",
      vendor: "SalonHub Software",
      description: "Monthly subscription",
      amount: 99.0,
      paymentMethod: "Credit Card",
      status: "Paid",
    },
  ]

  // Filter expenses based on search term and location
  const filteredExpenses = expenses.filter((expense) => {
    // Filter by search term
    if (
      search &&
      !expense.vendor.toLowerCase().includes(search.toLowerCase()) &&
      !expense.description.toLowerCase().includes(search.toLowerCase()) &&
      !expense.category.toLowerCase().includes(search.toLowerCase()) &&
      !expense.id.toLowerCase().includes(search.toLowerCase())
    ) {
      return false
    }

    // Filter by location
    if (selectedLocation !== "all") {
      // Special handling for home service location
      if (selectedLocation === "home") {
        if (expense.location !== "home") {
          return false
        }
      } else if (expense.location !== selectedLocation) {
        return false
      }
    }

    return true
  })

  return (
    <Card>
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Expense ID</TableHead>
              <TableHead>Date</TableHead>
              <TableHead>Category</TableHead>
              <TableHead>Vendor</TableHead>
              <TableHead>Description</TableHead>
              <TableHead>Amount</TableHead>
              <TableHead>Payment Method</TableHead>
              <TableHead>Location</TableHead>
              <TableHead>Status</TableHead>
              <TableHead className="text-right">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredExpenses.length === 0 ? (
              <TableRow>
                <TableCell colSpan={10} className="h-24 text-center">
                  No expenses found.
                </TableCell>
              </TableRow>
            ) : (
              filteredExpenses.map((expense) => (
                <TableRow key={expense.id}>
                  <TableCell className="font-medium">{expense.id}</TableCell>
                  <TableCell>{expense.date}</TableCell>
                  <TableCell>{expense.category}</TableCell>
                  <TableCell>{expense.vendor}</TableCell>
                  <TableCell>{expense.description}</TableCell>
                  <TableCell><CurrencyDisplay amount={expense.amount} /></TableCell>
                  <TableCell>{expense.paymentMethod}</TableCell>
                  <TableCell>
                    {expense.location === "loc1" ? "Downtown Salon" :
                     expense.location === "loc2" ? "Westside Salon" :
                     expense.location === "loc3" ? "Northside Salon" :
                     expense.location === "home" ? "Home Service" : expense.location}
                  </TableCell>
                  <TableCell>
                    <Badge variant={expense.status === "Paid" ? "success" : "outline"}>{expense.status}</Badge>
                  </TableCell>
                  <TableCell className="text-right">
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="outline" size="icon">
                          <MoreHorizontal className="h-4 w-4" />
                          <span className="sr-only">Open menu</span>
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem>
                          <Eye className="mr-2 h-4 w-4" />
                          View details
                        </DropdownMenuItem>
                        <DropdownMenuItem>Edit expense</DropdownMenuItem>
                        <DropdownMenuItem>Download receipt</DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </div>
    </Card>
  )
}

