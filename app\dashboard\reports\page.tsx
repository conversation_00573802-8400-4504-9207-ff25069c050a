"use client"

import { useState } from "react"
import { useAuth } from "@/lib/auth-provider"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rigger } from "@/components/ui/tabs"
import { DatePickerWithRange } from "@/components/date-range-picker"
import { SalesChart } from "@/components/reports/sales-chart"
import { AppointmentsChart } from "@/components/reports/appointments-chart"
import { StaffPerformanceTable } from "@/components/reports/staff-performance-table"
import { ServicePopularityChart } from "@/components/reports/service-popularity-chart"
import { ClientRetentionChart } from "@/components/reports/client-retention-chart"
import { ProductSalesChart } from "@/components/reports/product-sales-chart"
import { InventoryAnalytics } from "@/components/reports/inventory-analytics"
import { PaymentMethodChart } from "@/components/reports/payment-method-chart"
import { PaymentMethodTable } from "@/components/reports/payment-method-table"
import { CurrencyDisplay } from "@/components/ui/currency-display"
import { FileDown, Printer } from "lucide-react"
import { format, subDays } from "date-fns"
import type { DateRange } from "react-day-picker"

export default function ReportsPage() {
  const { user, currentLocation } = useAuth()
  const [dateRange, setDateRange] = useState<DateRange | undefined>({
    from: subDays(new Date(), 30),
    to: new Date(),
  })

  // Only certain roles can access this page
  if (user?.role !== "super_admin" && user?.role !== "org_admin" && user?.role !== "location_manager") {
    return (
      <div className="flex h-full items-center justify-center">
        <Card className="max-w-md">
          <CardHeader>
            <CardTitle>Access Denied</CardTitle>
            <CardDescription>You don't have permission to view the reports page.</CardDescription>
          </CardHeader>
        </Card>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">Reports & Analytics</h2>
          <p className="text-muted-foreground">
            {currentLocation === "all"
              ? "View reports across all locations"
              : `View reports for ${currentLocation === "loc1" ? "Downtown" : currentLocation === "loc2" ? "Westside" : "Northside"} location`}
          </p>
        </div>
        <div className="flex items-center gap-2">
          <DatePickerWithRange dateRange={dateRange} onDateRangeChange={setDateRange} />
          <Button variant="outline">
            <FileDown className="mr-2 h-4 w-4" />
            Export
          </Button>
          <Button variant="outline" size="icon">
            <Printer className="h-4 w-4" />
          </Button>
        </div>
      </div>

      <Tabs defaultValue="overview">
        <TabsList className="mb-4">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="sales">Sales</TabsTrigger>
          <TabsTrigger value="products">Products</TabsTrigger>
          <TabsTrigger value="inventory">Inventory</TabsTrigger>
          <TabsTrigger value="appointments">Appointments</TabsTrigger>
          <TabsTrigger value="staff">Staff Performance</TabsTrigger>
          <TabsTrigger value="clients">Client Analytics</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">Total Revenue</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold"><CurrencyDisplay amount={12458.75} /></div>
                <div className="flex flex-col gap-1 mt-2">
                  <div className="flex items-center justify-between">
                    <span className="text-xs text-muted-foreground">Card</span>
                    <span className="text-xs font-medium"><CurrencyDisplay amount={7475.25} /></span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-xs text-muted-foreground">Cash</span>
                    <span className="text-xs font-medium"><CurrencyDisplay amount={3737.63} /></span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-xs text-muted-foreground">Mobile</span>
                    <span className="text-xs font-medium"><CurrencyDisplay amount={1245.87} /></span>
                  </div>
                </div>
                <p className="text-xs text-emerald-500 mt-2">
                  <span>+12.5%</span> from previous period
                </p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">Appointments</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">248</div>
                <p className="text-xs text-muted-foreground">
                  <span className="text-emerald-500">****%</span> from previous period
                </p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">New Clients</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">42</div>
                <p className="text-xs text-muted-foreground">
                  <span className="text-emerald-500">+8.1%</span> from previous period
                </p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">Retention Rate</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">78.3%</div>
                <p className="text-xs text-muted-foreground">
                  <span className="text-emerald-500">+2.4%</span> from previous period
                </p>
              </CardContent>
            </Card>
          </div>

          <div className="grid gap-6 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>Revenue Overview</CardTitle>
                <CardDescription>
                  {dateRange?.from && dateRange?.to
                    ? `${format(dateRange.from, "LLL dd, y")} - ${format(dateRange.to, "LLL dd, y")}`
                    : "Select a date range"}
                </CardDescription>
              </CardHeader>
              <CardContent>
                <SalesChart dateRange={dateRange} />
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Appointment Volume</CardTitle>
                <CardDescription>Number of appointments over time</CardDescription>
              </CardHeader>
              <CardContent>
                <AppointmentsChart dateRange={dateRange} />
              </CardContent>
            </Card>
          </div>

          <Card>
            <CardHeader>
              <CardTitle>Staff Performance</CardTitle>
              <CardDescription>Top performing staff members</CardDescription>
            </CardHeader>
            <CardContent>
              <StaffPerformanceTable dateRange={dateRange} limit={5} />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="sales" className="space-y-6">
          <div className="grid gap-6 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>Revenue by Day</CardTitle>
                <CardDescription>Daily revenue breakdown</CardDescription>
              </CardHeader>
              <CardContent>
                <SalesChart dateRange={dateRange} groupBy="day" />
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Revenue by Category</CardTitle>
                <CardDescription>Revenue distribution by service category</CardDescription>
              </CardHeader>
              <CardContent className="h-[350px]">
                <ServicePopularityChart dateRange={dateRange} type="revenue" />
              </CardContent>
            </Card>
          </div>

          <div className="grid gap-6 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>Revenue by Payment Method</CardTitle>
                <CardDescription>Breakdown of revenue by payment type</CardDescription>
              </CardHeader>
              <CardContent className="h-[350px]">
                <PaymentMethodChart dateRange={dateRange} />
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Payment Method Details</CardTitle>
                <CardDescription>Detailed payment method statistics</CardDescription>
              </CardHeader>
              <CardContent>
                <PaymentMethodTable dateRange={dateRange} />
              </CardContent>
            </Card>
          </div>

          <Card>
            <CardHeader>
              <CardTitle>Product Sales</CardTitle>
              <CardDescription>Top selling retail products</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="rounded-md border">
                <table className="w-full">
                  <thead>
                    <tr className="border-b bg-muted/50">
                      <th className="p-3 text-left font-medium">Product</th>
                      <th className="p-3 text-left font-medium">Category</th>
                      <th className="p-3 text-right font-medium">Units Sold</th>
                      <th className="p-3 text-right font-medium">Revenue</th>
                      <th className="p-3 text-right font-medium">Profit</th>
                    </tr>
                  </thead>
                  <tbody>
                    {[
                      {
                        name: "Shampoo - Professional",
                        category: "Hair Care",
                        units: 42,
                        revenue: 1049.58,
                        profit: 524.79,
                      },
                      {
                        name: "Conditioner - Professional",
                        category: "Hair Care",
                        units: 38,
                        revenue: 873.62,
                        profit: 427.5,
                      },
                      { name: "Styling Gel", category: "Styling", units: 35, revenue: 664.65, profit: 332.5 },
                      { name: "Hair Spray", category: "Styling", units: 31, revenue: 526.69, profit: 255.75 },
                      { name: "Treatment Mask", category: "Treatments", units: 24, revenue: 791.76, profit: 396.0 },
                    ].map((product, i) => (
                      <tr key={i} className="border-b">
                        <td className="p-3 font-medium">{product.name}</td>
                        <td className="p-3">{product.category}</td>
                        <td className="p-3 text-right">{product.units}</td>
                        <td className="p-3 text-right"><CurrencyDisplay amount={product.revenue} /></td>
                        <td className="p-3 text-right"><CurrencyDisplay amount={product.profit} /></td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="products" className="space-y-6">
          <ProductSalesChart dateRange={dateRange} />
        </TabsContent>

        <TabsContent value="inventory" className="space-y-6">
          <InventoryAnalytics dateRange={dateRange} />
        </TabsContent>

        <TabsContent value="appointments" className="space-y-6">
          <div className="grid gap-6 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>Appointment Volume</CardTitle>
                <CardDescription>Number of appointments over time</CardDescription>
              </CardHeader>
              <CardContent>
                <AppointmentsChart dateRange={dateRange} />
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Service Popularity</CardTitle>
                <CardDescription>Most booked services</CardDescription>
              </CardHeader>
              <CardContent className="h-[350px]">
                <ServicePopularityChart dateRange={dateRange} type="count" />
              </CardContent>
            </Card>
          </div>

          <Card>
            <CardHeader>
              <CardTitle>Booking Source</CardTitle>
              <CardDescription>How clients are making appointments</CardDescription>
            </CardHeader>
            <CardContent className="h-[350px]">
              <div className="flex items-center justify-center h-full">
                <div className="w-full max-w-md">
                  <div className="space-y-4">
                    {[
                      { source: "Online Booking", percentage: 45, count: 112 },
                      { source: "Phone", percentage: 30, count: 74 },
                      { source: "Walk-in", percentage: 15, count: 37 },
                      { source: "Mobile App", percentage: 10, count: 25 },
                    ].map((source, i) => (
                      <div key={i} className="space-y-1">
                        <div className="flex justify-between text-sm">
                          <span>{source.source}</span>
                          <span>
                            {source.percentage}% ({source.count})
                          </span>
                        </div>
                        <div className="w-full bg-muted rounded-full h-2.5">
                          <div
                            className="bg-primary h-2.5 rounded-full"
                            style={{ width: `${source.percentage}%` }}
                          ></div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="staff" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Staff Performance</CardTitle>
              <CardDescription>Revenue and appointments by staff member</CardDescription>
            </CardHeader>
            <CardContent>
              <StaffPerformanceTable dateRange={dateRange} />
            </CardContent>
          </Card>

          <div className="grid gap-6 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>Service Distribution</CardTitle>
                <CardDescription>Services performed by staff</CardDescription>
              </CardHeader>
              <CardContent className="h-[350px]">
                <div className="flex items-center justify-center h-full">
                  <div className="w-full max-w-md">
                    <div className="space-y-4">
                      {[
                        { name: "Haircut & Style", percentage: 35 },
                        { name: "Color & Highlights", percentage: 25 },
                        { name: "Treatments", percentage: 15 },
                        { name: "Styling", percentage: 15 },
                        { name: "Other", percentage: 10 },
                      ].map((service, i) => (
                        <div key={i} className="space-y-1">
                          <div className="flex justify-between text-sm">
                            <span>{service.name}</span>
                            <span>{service.percentage}%</span>
                          </div>
                          <div className="w-full bg-muted rounded-full h-2.5">
                            <div
                              className="bg-primary h-2.5 rounded-full"
                              style={{ width: `${service.percentage}%` }}
                            ></div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Utilization Rate</CardTitle>
                <CardDescription>Staff booking efficiency</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="rounded-md border">
                  <table className="w-full">
                    <thead>
                      <tr className="border-b bg-muted/50">
                        <th className="p-3 text-left font-medium">Staff Member</th>
                        <th className="p-3 text-right font-medium">Utilization</th>
                        <th className="p-3 text-right font-medium">Hours Booked</th>
                        <th className="p-3 text-right font-medium">Available Hours</th>
                      </tr>
                    </thead>
                    <tbody>
                      {[
                        { name: "Alex Johnson", utilization: 92, hoursBooked: 147, availableHours: 160 },
                        { name: "Maria Garcia", utilization: 88, hoursBooked: 141, availableHours: 160 },
                        { name: "David Kim", utilization: 85, hoursBooked: 136, availableHours: 160 },
                        { name: "Sarah Chen", utilization: 78, hoursBooked: 125, availableHours: 160 },
                        { name: "James Wilson", utilization: 75, hoursBooked: 120, availableHours: 160 },
                      ].map((staff, i) => (
                        <tr key={i} className="border-b">
                          <td className="p-3 font-medium">{staff.name}</td>
                          <td className="p-3 text-right">{staff.utilization}%</td>
                          <td className="p-3 text-right">{staff.hoursBooked}</td>
                          <td className="p-3 text-right">{staff.availableHours}</td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="clients" className="space-y-6">
          <div className="grid gap-6 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>New vs. Returning Clients</CardTitle>
                <CardDescription>Client acquisition and retention</CardDescription>
              </CardHeader>
              <CardContent className="h-[350px]">
                <ClientRetentionChart dateRange={dateRange} />
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Client Retention Rate</CardTitle>
                <CardDescription>Percentage of clients who return</CardDescription>
              </CardHeader>
              <CardContent className="h-[350px]">
                <div className="flex flex-col items-center justify-center h-full">
                  <div className="relative w-48 h-48">
                    <svg className="w-full h-full" viewBox="0 0 100 100">
                      <circle
                        className="text-muted stroke-current"
                        strokeWidth="10"
                        cx="50"
                        cy="50"
                        r="40"
                        fill="transparent"
                      ></circle>
                      <circle
                        className="text-primary stroke-current"
                        strokeWidth="10"
                        strokeLinecap="round"
                        cx="50"
                        cy="50"
                        r="40"
                        fill="transparent"
                        strokeDasharray="251.2"
                        strokeDashoffset="54"
                        transform="rotate(-90 50 50)"
                      ></circle>
                    </svg>
                    <div className="absolute inset-0 flex flex-col items-center justify-center">
                      <span className="text-4xl font-bold">78%</span>
                      <span className="text-sm text-muted-foreground">Retention Rate</span>
                    </div>
                  </div>
                  <div className="mt-8 text-center">
                    <p className="text-sm text-muted-foreground">
                      78% of clients return within 60 days of their first visit
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          <Card>
            <CardHeader>
              <CardTitle>Client Lifetime Value</CardTitle>
              <CardDescription>Average revenue per client over time</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="rounded-md border">
                <table className="w-full">
                  <thead>
                    <tr className="border-b bg-muted/50">
                      <th className="p-3 text-left font-medium">Client Segment</th>
                      <th className="p-3 text-right font-medium">Avg. Visits</th>
                      <th className="p-3 text-right font-medium">Avg. Spend</th>
                      <th className="p-3 text-right font-medium">Lifetime Value</th>
                    </tr>
                  </thead>
                  <tbody>
                    {[
                      { segment: "VIP Clients", visits: 12.4, spend: 185, ltv: 2294 },
                      { segment: "Regular Clients", visits: 8.2, spend: 120, ltv: 984 },
                      { segment: "Occasional Clients", visits: 3.5, spend: 95, ltv: 332.5 },
                      { segment: "New Clients", visits: 1.0, spend: 85, ltv: 85 },
                      { segment: "All Clients", visits: 6.3, spend: 115, ltv: 724.5 },
                    ].map((segment, i) => (
                      <tr key={i} className="border-b">
                        <td className="p-3 font-medium">{segment.segment}</td>
                        <td className="p-3 text-right">{segment.visits}</td>
                        <td className="p-3 text-right"><CurrencyDisplay amount={segment.spend} /></td>
                        <td className="p-3 text-right"><CurrencyDisplay amount={segment.ltv} /></td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}

