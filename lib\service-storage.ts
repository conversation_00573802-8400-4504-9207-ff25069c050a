"use client"

import { mockServices, mockCategories } from "./mock-data"

// Storage keys for localStorage
const STORAGE_KEYS = {
  SERVICES: "vanity_services",
  CATEGORIES: "vanity_service_categories"
}

// Service interface
export interface Service {
  id: string
  name: string
  category: string
  price: number
  duration: number
  locations: string[]
  description?: string
}

// Service category interface
export interface ServiceCategory {
  id: string
  name: string
  description: string
  serviceCount: number
}

// Helper function to get data from localStorage
function getFromStorage<T>(key: string, defaultValue: T): T {
  if (typeof window === 'undefined') {
    return defaultValue
  }

  try {
    const storedValue = localStorage.getItem(key)
    if (!storedValue) {
      return defaultValue
    }

    try {
      const parsedValue = JSON.parse(storedValue)

      // For arrays, ensure we actually got an array back
      if (Array.isArray(defaultValue) && !Array.isArray(parsedValue)) {
        console.warn(`Expected array for ${key} but got ${typeof parsedValue}`)
        return defaultValue
      }

      // Special handling for services to ensure no invalid locations
      if (key === STORAGE_KEYS.SERVICES && Array.isArray(parsedValue)) {
        // Fix any services with invalid locations (loc4)
        const fixedServices = parsedValue.map((service: any) => {
          if (service && Array.isArray(service.locations)) {
            service.locations = service.locations.map((loc: string) =>
              loc === 'loc4' ? 'home' : loc
            );
          }
          return service;
        });
        return fixedServices as T;
      }

      return parsedValue as T
    } catch (parseError) {
      console.error(`Error parsing JSON for ${key} from localStorage:`, parseError)
      return defaultValue
    }
  } catch (error) {
    console.error(`Error retrieving ${key} from localStorage:`, error)
    return defaultValue
  }
}

// Helper function to save data to localStorage
function saveToStorage<T>(key: string, value: T): void {
  if (typeof window === 'undefined') {
    return
  }

  try {
    // Special handling for services to ensure no invalid locations
    if (key === STORAGE_KEYS.SERVICES && Array.isArray(value)) {
      // Fix any services with invalid locations (loc4)
      const fixedServices = (value as any[]).map(service => {
        if (service && Array.isArray(service.locations)) {
          service.locations = service.locations.map((loc: string) =>
            loc === 'loc4' ? 'home' : loc
          );
        }
        return service;
      });
      localStorage.setItem(key, JSON.stringify(fixedServices))
    } else {
      localStorage.setItem(key, JSON.stringify(value))
    }
  } catch (error) {
    console.error(`Error saving ${key} to localStorage:`, error)
  }
}

// Service Storage Service
export const ServiceStorage = {
  // Services
  getServices: (): Service[] => getFromStorage<Service[]>(STORAGE_KEYS.SERVICES, mockServices),

  saveServices: (services: Service[]) => saveToStorage(STORAGE_KEYS.SERVICES, services),

  addService: (service: Service) => {
    const services = ServiceStorage.getServices()
    services.push(service)
    saveToStorage(STORAGE_KEYS.SERVICES, services)
  },

  updateService: (updatedService: Service) => {
    if (!updatedService || !updatedService.id) {
      console.error("Cannot update service: Invalid service data")
      return
    }

    try {
      const services = ServiceStorage.getServices()

      if (!Array.isArray(services)) {
        console.error("Cannot update service: Services is not an array")
        return
      }

      const index = services.findIndex(service => service.id === updatedService.id)

      if (index !== -1) {
        // Ensure locations is an array
        if (!Array.isArray(updatedService.locations)) {
          console.warn("Service locations is not an array, fixing")
          updatedService.locations = updatedService.locations ? [updatedService.locations as any] : []
        }

        services[index] = updatedService
        saveToStorage(STORAGE_KEYS.SERVICES, services)
        console.log("Service updated in storage:", updatedService.id, updatedService.name)
      } else {
        console.warn("Service not found in storage, cannot update:", updatedService.id)
      }
    } catch (error) {
      console.error("Error updating service in storage:", error)
    }
  },

  deleteService: (serviceId: string) => {
    if (!serviceId) {
      console.error("Cannot delete service: Invalid service ID")
      return
    }

    try {
      const services = ServiceStorage.getServices()

      if (!Array.isArray(services)) {
        console.error("Cannot delete service: Services is not an array")
        return
      }

      const serviceToDelete = services.find(service => service.id === serviceId)

      if (serviceToDelete) {
        console.log("Found service to delete:", serviceId, serviceToDelete.name)
      } else {
        console.warn("Service not found in storage, proceeding with deletion anyway:", serviceId)
      }

      const filteredServices = services.filter(service => service.id !== serviceId)
      saveToStorage(STORAGE_KEYS.SERVICES, filteredServices)
      console.log("Service deleted from storage:", serviceId)
    } catch (error) {
      console.error("Error deleting service from storage:", error)
    }
  },

  // Service Categories
  getServiceCategories: (): ServiceCategory[] => getFromStorage<ServiceCategory[]>(STORAGE_KEYS.CATEGORIES, mockCategories),

  saveServiceCategories: (categories: ServiceCategory[]) => saveToStorage(STORAGE_KEYS.CATEGORIES, categories),

  addServiceCategory: (category: ServiceCategory) => {
    const categories = ServiceStorage.getServiceCategories()
    categories.push(category)
    saveToStorage(STORAGE_KEYS.CATEGORIES, categories)
  },

  updateServiceCategory: (updatedCategory: ServiceCategory) => {
    const categories = ServiceStorage.getServiceCategories()
    const index = categories.findIndex(category => category.id === updatedCategory.id)
    if (index !== -1) {
      categories[index] = updatedCategory
      saveToStorage(STORAGE_KEYS.CATEGORIES, categories)
    }
  },

  deleteServiceCategory: (categoryId: string) => {
    const categories = ServiceStorage.getServiceCategories()
    const filteredCategories = categories.filter(category => category.id !== categoryId)
    saveToStorage(STORAGE_KEYS.CATEGORIES, filteredCategories)
  }
}
