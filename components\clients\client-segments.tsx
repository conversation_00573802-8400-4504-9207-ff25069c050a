"use client"

import { useRouter } from "next/navigation"
import { Card, Card<PERSON>ontent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { useClients } from "@/lib/client-provider"
import { Button } from "@/components/ui/button"
import { Eye, Edit, MoreHorizontal, UserRound } from "lucide-react"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { Badge } from "@/components/ui/badge"
import { CurrencyDisplay } from "@/components/ui/currency-display"

export function ClientSegments() {
  const router = useRouter()
  const { clients } = useClients()

  // Filter clients by segment
  const vipClients = clients.filter((client) => client.segment === "VIP")
  const regularClients = clients.filter((client) => client.segment === "Regular")
  const newClients = clients.filter((client) => client.segment === "New")
  const atRiskClients = clients.filter((client) => client.segment === "At Risk")

  const handleViewClient = (clientId: string) => {
    router.push(`/dashboard/clients/${clientId}`)
  }

  const renderClientTable = (clients: any[]) => (
    <div className="rounded-md border">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>Name</TableHead>
            <TableHead>Email</TableHead>
            <TableHead>Last Visit</TableHead>
            <TableHead>Total Spent</TableHead>
            <TableHead>Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {clients.length === 0 ? (
            <TableRow>
              <TableCell colSpan={5} className="h-24 text-center">
                No clients in this segment.
              </TableCell>
            </TableRow>
          ) : (
            clients.map((client) => (
              <TableRow key={client.id}>
                <TableCell>
                  <div className="flex items-center gap-2">
                    <Button
                      variant="ghost"
                      size="icon"
                      className="h-8 w-8 rounded-full"
                      onClick={() => handleViewClient(client.id)}
                    >
                      <UserRound className="h-4 w-4" />
                    </Button>
                    <span className="font-medium">{client.name}</span>
                  </div>
                </TableCell>
                <TableCell>{client.email}</TableCell>
                <TableCell>{client.lastVisit}</TableCell>
                <TableCell>
                  {client.totalSpent ? (
                    <CurrencyDisplay amount={client.totalSpent} />
                  ) : (
                    <CurrencyDisplay amount={0} />
                  )}
                </TableCell>
                <TableCell>
                  <div className="flex items-center gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleViewClient(client.id)}
                    >
                      <Eye className="mr-2 h-4 w-4" />
                      View Profile
                    </Button>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="icon">
                          <MoreHorizontal className="h-4 w-4" />
                          <span className="sr-only">Open menu</span>
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem onClick={() => handleViewClient(client.id)}>
                          <Eye className="mr-2 h-4 w-4" />
                          View details
                        </DropdownMenuItem>
                        <DropdownMenuItem>
                          <Edit className="mr-2 h-4 w-4" />
                          Edit segment
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>
                </TableCell>
              </TableRow>
            ))
          )}
        </TableBody>
      </Table>
    </div>
  )

  return (
    <Card>
      <CardHeader>
        <CardTitle>Client Segments</CardTitle>
        <CardDescription>Manage your client segments for targeted marketing and services</CardDescription>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="vip">
          <TabsList className="mb-4">
            <TabsTrigger value="vip">VIP Clients</TabsTrigger>
            <TabsTrigger value="regular">Regular Clients</TabsTrigger>
            <TabsTrigger value="new">New Clients</TabsTrigger>
            <TabsTrigger value="at-risk">At Risk</TabsTrigger>
          </TabsList>

          <TabsContent value="vip">{renderClientTable(vipClients)}</TabsContent>

          <TabsContent value="regular">{renderClientTable(regularClients)}</TabsContent>

          <TabsContent value="new">{renderClientTable(newClients)}</TabsContent>

          <TabsContent value="at-risk">{renderClientTable(atRiskClients)}</TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  )
}

