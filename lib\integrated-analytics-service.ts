"use client"

import { Transaction, TransactionType, TransactionSource } from "./transaction-types"
import { inventoryTransactionService } from "./inventory-transaction-service"

/**
 * Comprehensive analytics interface combining all business metrics
 */
export interface IntegratedAnalytics {
  // Revenue Analytics
  totalRevenue: number;
  serviceRevenue: number;
  productRevenue: number;
  revenueGrowth: number;
  
  // Expense Analytics
  totalExpenses: number;
  operatingExpenses: number;
  cogs: number;
  expenseGrowth: number;
  
  // Profit Analytics
  grossProfit: number;
  netProfit: number;
  grossMargin: number;
  netMargin: number;
  
  // Inventory Analytics
  inventoryValue: number;
  inventoryTurnover: number;
  lowStockItems: number;
  outOfStockItems: number;
  
  // Product Performance
  topSellingProducts: Array<{
    id: string;
    name: string;
    quantitySold: number;
    revenue: number;
    profit: number;
    margin: number;
  }>;
  
  // Service Performance
  topServices: Array<{
    id: string;
    name: string;
    bookings: number;
    revenue: number;
    averagePrice: number;
  }>;
  
  // Financial Health Indicators
  revenuePerClient: number;
  averageTransactionValue: number;
  profitPerTransaction: number;
  cashFlow: number;
}

/**
 * Service for integrated analytics across all business functions
 */
export class IntegratedAnalyticsService {
  private transactions: Transaction[] = [];
  private listeners: Array<(analytics: IntegratedAnalytics) => void> = [];

  constructor() {
    this.loadTransactions();
  }

  /**
   * Load transactions from localStorage
   */
  private loadTransactions() {
    if (typeof window !== 'undefined') {
      const stored = localStorage.getItem('vanity_transactions');
      if (stored) {
        try {
          this.transactions = JSON.parse(stored).map((t: any) => ({
            ...t,
            date: new Date(t.date),
            createdAt: new Date(t.createdAt),
            updatedAt: new Date(t.updatedAt)
          }));
        } catch (error) {
          console.error('Failed to load transactions:', error);
        }
      }
    }
  }

  /**
   * Save transactions to localStorage
   */
  private saveTransactions() {
    if (typeof window !== 'undefined') {
      localStorage.setItem('vanity_transactions', JSON.stringify(this.transactions));
    }
  }

  /**
   * Add a new transaction and update analytics
   */
  addTransaction(transaction: Transaction) {
    this.transactions.push(transaction);
    this.saveTransactions();
    this.notifyListeners();
  }

  /**
   * Subscribe to analytics updates
   */
  subscribe(listener: (analytics: IntegratedAnalytics) => void) {
    this.listeners.push(listener);
    return () => {
      this.listeners = this.listeners.filter(l => l !== listener);
    };
  }

  /**
   * Notify all listeners of analytics updates
   */
  private notifyListeners() {
    const analytics = this.getAnalytics();
    this.listeners.forEach(listener => listener(analytics));
  }

  /**
   * Get comprehensive analytics for a date range
   */
  getAnalytics(
    startDate?: Date,
    endDate?: Date,
    locationId?: string
  ): IntegratedAnalytics {
    const now = new Date();
    const defaultStart = new Date(now.getFullYear(), now.getMonth(), 1);
    const defaultEnd = now;

    const start = startDate || defaultStart;
    const end = endDate || defaultEnd;

    // Filter transactions by date range and location
    const filteredTransactions = this.transactions.filter(t => {
      const transactionDate = new Date(t.date);
      const inDateRange = transactionDate >= start && transactionDate <= end;
      const inLocation = !locationId || t.location === locationId;
      return inDateRange && inLocation;
    });

    // Calculate revenue metrics
    const serviceRevenue = filteredTransactions
      .filter(t => t.type === TransactionType.INCOME && t.source !== TransactionSource.INVENTORY)
      .reduce((sum, t) => sum + t.amount, 0);

    const productRevenue = filteredTransactions
      .filter(t => t.type === TransactionType.INVENTORY_SALE)
      .reduce((sum, t) => sum + t.amount, 0);

    const totalRevenue = serviceRevenue + productRevenue;

    // Calculate expense metrics
    const operatingExpenses = filteredTransactions
      .filter(t => t.type === TransactionType.EXPENSE)
      .reduce((sum, t) => sum + t.amount, 0);

    const cogs = filteredTransactions
      .filter(t => t.type === TransactionType.COGS)
      .reduce((sum, t) => sum + t.amount, 0);

    const inventoryPurchases = filteredTransactions
      .filter(t => t.type === TransactionType.INVENTORY_PURCHASE)
      .reduce((sum, t) => sum + t.amount, 0);

    const totalExpenses = operatingExpenses + cogs + inventoryPurchases;

    // Calculate profit metrics
    const grossProfit = totalRevenue - cogs;
    const netProfit = totalRevenue - totalExpenses;
    const grossMargin = totalRevenue > 0 ? (grossProfit / totalRevenue) * 100 : 0;
    const netMargin = totalRevenue > 0 ? (netProfit / totalRevenue) * 100 : 0;

    // Get inventory analytics
    const inventoryAnalytics = inventoryTransactionService.getInventoryAnalytics(start, end, locationId);

    // Calculate top selling products
    const productSales = new Map<string, {
      name: string;
      quantitySold: number;
      revenue: number;
      profit: number;
    }>();

    filteredTransactions
      .filter(t => t.type === TransactionType.INVENTORY_SALE && t.productId)
      .forEach(t => {
        const existing = productSales.get(t.productId!) || {
          name: t.productName || 'Unknown Product',
          quantitySold: 0,
          revenue: 0,
          profit: 0
        };
        existing.quantitySold += t.quantity || 0;
        existing.revenue += t.amount;
        existing.profit += (t.amount - (t.costPrice || 0) * (t.quantity || 0));
        productSales.set(t.productId!, existing);
      });

    const topSellingProducts = Array.from(productSales.entries())
      .map(([id, data]) => ({
        id,
        name: data.name,
        quantitySold: data.quantitySold,
        revenue: data.revenue,
        profit: data.profit,
        margin: data.revenue > 0 ? (data.profit / data.revenue) * 100 : 0
      }))
      .sort((a, b) => b.revenue - a.revenue)
      .slice(0, 10);

    // Calculate service performance (mock data for now)
    const topServices = [
      { id: '1', name: 'Haircut & Style', bookings: 45, revenue: 3375, averagePrice: 75 },
      { id: '2', name: 'Color & Highlights', bookings: 22, revenue: 3300, averagePrice: 150 },
      { id: '3', name: 'Blowout', bookings: 18, revenue: 1170, averagePrice: 65 },
      { id: '4', name: 'Hair Treatment', bookings: 15, revenue: 1875, averagePrice: 125 },
      { id: '5', name: 'Styling', bookings: 12, revenue: 720, averagePrice: 60 }
    ];

    // Calculate financial health indicators
    const transactionCount = filteredTransactions.filter(t => 
      t.type === TransactionType.INCOME || t.type === TransactionType.INVENTORY_SALE
    ).length;

    const uniqueClients = new Set(
      filteredTransactions
        .filter(t => t.clientId)
        .map(t => t.clientId)
    ).size;

    const revenuePerClient = uniqueClients > 0 ? totalRevenue / uniqueClients : 0;
    const averageTransactionValue = transactionCount > 0 ? totalRevenue / transactionCount : 0;
    const profitPerTransaction = transactionCount > 0 ? netProfit / transactionCount : 0;

    // Calculate growth rates (mock for now - would compare to previous period)
    const revenueGrowth = 15.3; // Mock growth percentage
    const expenseGrowth = 8.7;

    return {
      totalRevenue,
      serviceRevenue,
      productRevenue,
      revenueGrowth,
      totalExpenses,
      operatingExpenses,
      cogs,
      expenseGrowth,
      grossProfit,
      netProfit,
      grossMargin,
      netMargin,
      inventoryValue: inventoryAnalytics.totalInventoryValue,
      inventoryTurnover: 6.2, // Mock value
      lowStockItems: inventoryAnalytics.lowStockItems.length,
      outOfStockItems: 3, // Mock value
      topSellingProducts,
      topServices,
      revenuePerClient,
      averageTransactionValue,
      profitPerTransaction,
      cashFlow: netProfit // Simplified cash flow calculation
    };
  }

  /**
   * Get revenue breakdown by source
   */
  getRevenueBreakdown(startDate?: Date, endDate?: Date, locationId?: string) {
    const analytics = this.getAnalytics(startDate, endDate, locationId);
    
    return {
      services: {
        amount: analytics.serviceRevenue,
        percentage: analytics.totalRevenue > 0 ? (analytics.serviceRevenue / analytics.totalRevenue) * 100 : 0
      },
      products: {
        amount: analytics.productRevenue,
        percentage: analytics.totalRevenue > 0 ? (analytics.productRevenue / analytics.totalRevenue) * 100 : 0
      }
    };
  }

  /**
   * Get expense breakdown by category
   */
  getExpenseBreakdown(startDate?: Date, endDate?: Date, locationId?: string) {
    const analytics = this.getAnalytics(startDate, endDate, locationId);
    
    return {
      operating: {
        amount: analytics.operatingExpenses,
        percentage: analytics.totalExpenses > 0 ? (analytics.operatingExpenses / analytics.totalExpenses) * 100 : 0
      },
      cogs: {
        amount: analytics.cogs,
        percentage: analytics.totalExpenses > 0 ? (analytics.cogs / analytics.totalExpenses) * 100 : 0
      }
    };
  }

  /**
   * Get profit margin comparison
   */
  getProfitMarginComparison(startDate?: Date, endDate?: Date, locationId?: string) {
    const analytics = this.getAnalytics(startDate, endDate, locationId);
    
    // Calculate service-only margin (excluding product costs)
    const serviceMargin = analytics.serviceRevenue > 0 
      ? ((analytics.serviceRevenue - analytics.operatingExpenses) / analytics.serviceRevenue) * 100 
      : 0;

    // Calculate product-only margin
    const productMargin = analytics.productRevenue > 0 
      ? ((analytics.productRevenue - analytics.cogs) / analytics.productRevenue) * 100 
      : 0;

    return {
      serviceMargin,
      productMargin,
      combinedMargin: analytics.grossMargin,
      netMargin: analytics.netMargin
    };
  }
}

// Export singleton instance
export const integratedAnalyticsService = new IntegratedAnalyticsService();
