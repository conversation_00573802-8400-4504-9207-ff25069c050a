"use client"

import type React from "react"

import { useState, useEffect } from "react"
import { useAuth } from "@/lib/auth-provider"
import { useCurrency } from "@/lib/currency-provider"
import { useLocations } from "@/lib/location-provider"
import { useServices } from "@/lib/service-provider"
import { Button } from "@/components/ui/button"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Checkbox } from "@/components/ui/checkbox"
import { useToast } from "@/components/ui/use-toast"

interface NewServiceDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
}

export function NewServiceDialog({ open, onOpenChange }: NewServiceDialogProps) {
  const { currentLocation } = useAuth()
  const { toast } = useToast()
  const { currency } = useCurrency()
  const { locations, getLocationName } = useLocations()
  const { addService, categories } = useServices()
  const [isSubmitting, setIsSubmitting] = useState(false)

  const [formData, setFormData] = useState({
    name: "",
    category: "",
    description: "",
    duration: "60",
    price: "",
    locations: [] as string[],
  })

  const handleChange = (field: string, value: string | string[]) => {
    setFormData((prev) => ({ ...prev, [field]: value }))
  }

  const handleLocationChange = (locationId: string, checked: boolean) => {
    if (checked) {
      setFormData((prev) => ({
        ...prev,
        locations: [...prev.locations, locationId],
      }))
    } else {
      setFormData((prev) => ({
        ...prev,
        locations: prev.locations.filter((id) => id !== locationId),
      }))
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsSubmitting(true)

    try {
      // Validate form data
      if (!formData.category) {
        toast({
          variant: "destructive",
          title: "Missing category",
          description: "Please select a category for the service.",
        })
        setIsSubmitting(false)
        return
      }

      if (formData.locations.length === 0) {
        toast({
          variant: "destructive",
          title: "Missing locations",
          description: "Please select at least one location for the service.",
        })
        setIsSubmitting(false)
        return
      }

      // Create new service object
      const newService = {
        name: formData.name,
        category: formData.category,
        price: Number.parseFloat(formData.price),
        duration: Number.parseInt(formData.duration),
        locations: formData.locations,
        description: formData.description
      }

      console.log("Creating new service:", newService)
      console.log("Available categories:", categories)

      // Find the category name for logging
      const categoryObj = categories.find(c => c.id === formData.category)
      console.log("Selected category:", categoryObj ? categoryObj.name : "Unknown category")

      // Add the service using the service provider
      const createdService = addService(newService)
      console.log("Service created successfully:", createdService)

      toast({
        title: "Service created",
        description: `${formData.name} has been added to your service catalog.`,
      })

      onOpenChange(false)
      setFormData({
        name: "",
        category: "",
        description: "",
        duration: "60",
        price: "",
        locations: [],
      })
    } catch (error) {
      console.error("Failed to create service:", error)
      toast({
        variant: "destructive",
        title: "Error",
        description: "Failed to create service. Please try again.",
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px]">
        <form onSubmit={handleSubmit}>
          <DialogHeader>
            <DialogTitle>Add New Service</DialogTitle>
            <DialogDescription>Add a new service to your salon's catalog. Fill in the details below.</DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="name">Service Name</Label>
                <Input
                  id="name"
                  value={formData.name}
                  onChange={(e) => handleChange("name", e.target.value)}
                  required
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="category">Category</Label>
                <Select value={formData.category} onValueChange={(value) => handleChange("category", value)}>
                  <SelectTrigger id="category">
                    <SelectValue placeholder="Select category" />
                  </SelectTrigger>
                  <SelectContent>
                    {categories.map((category) => (
                      <SelectItem key={category.id} value={category.id}>
                        {category.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="description">Description (Optional)</Label>
              <Textarea
                id="description"
                value={formData.description}
                onChange={(e) => handleChange("description", e.target.value)}
                rows={3}
              />
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="duration">Duration (minutes)</Label>
                <Input
                  id="duration"
                  type="number"
                  min="5"
                  step="5"
                  value={formData.duration}
                  onChange={(e) => handleChange("duration", e.target.value)}
                  required
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="price">Price ({currency.symbol})</Label>
                <Input
                  id="price"
                  type="number"
                  min="0"
                  step="0.01"
                  value={formData.price}
                  onChange={(e) => handleChange("price", e.target.value)}
                  required
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label>Available Locations</Label>
              <div className="grid grid-cols-2 gap-2">
                {locations.map((location) => (
                  <div key={location.id} className="flex items-center space-x-2">
                    <Checkbox
                      id={location.id}
                      checked={formData.locations.includes(location.id)}
                      onCheckedChange={(checked) => handleLocationChange(location.id, checked as boolean)}
                    />
                    <Label htmlFor={location.id}>{location.name}</Label>
                  </div>
                ))}
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="home"
                    checked={formData.locations.includes("home")}
                    onCheckedChange={(checked) => handleLocationChange("home", checked as boolean)}
                  />
                  <Label htmlFor="home">Home Service</Label>
                </div>
              </div>
            </div>
          </div>
          <DialogFooter>
            <Button type="button" variant="outline" onClick={() => onOpenChange(false)}>
              Cancel
            </Button>
            <Button type="submit" disabled={isSubmitting}>
              {isSubmitting ? "Creating..." : "Create Service"}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
}

