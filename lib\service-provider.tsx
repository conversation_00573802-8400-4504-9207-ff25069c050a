"use client"

import React, { createContext, useContext, useState, useEffect, useCallback, useMemo } from "react"
import { ServiceStorage, Service, ServiceCategory } from "@/lib/service-storage"
import { dataCache } from "@/lib/data-cache"
import { v4 as uuidv4 } from "uuid"

interface ServiceContextType {
  services: Service[]
  categories: ServiceCategory[]
  getServiceById: (id: string) => Service | undefined
  getCategoryById: (id: string) => ServiceCategory | undefined
  getCategoryName: (id: string) => string
  refreshServices: () => void
  refreshCategories: () => void
  addService: (service: Omit<Service, "id">) => Service
  updateService: (service: Service) => void
  deleteService: (serviceId: string) => void
  addCategory: (category: Omit<ServiceCategory, "id">) => ServiceCategory
  updateCategory: (category: ServiceCategory) => void
  deleteCategory: (categoryId: string) => void
}

const ServiceContext = createContext<ServiceContextType>({
  services: [],
  categories: [],
  getServiceById: () => undefined,
  getCategoryById: () => undefined,
  getCategoryName: () => "Uncategorized",
  refreshServices: () => {},
  refreshCategories: () => {},
  addService: () => ({ id: "", name: "", category: "", price: 0, duration: 0, locations: [] }),
  updateService: () => {},
  deleteService: () => {},
  addCategory: () => ({ id: "", name: "", description: "", serviceCount: 0 }),
  updateCategory: () => {},
  deleteCategory: () => {}
})

export function ServiceProvider({ children }: { children: React.ReactNode }) {
  const [services, setServices] = useState<Service[]>([])
  const [categories, setCategories] = useState<ServiceCategory[]>([])
  const [isInitialized, setIsInitialized] = useState(false)

  // Memoize services and categories retrieval functions with data caching
  const getStoredServices = useCallback(() => {
    return dataCache.getFromLocalStorage<Service[]>(
      "vanity_services",
      ServiceStorage.getServices(),
      { expiryTimeMs: 60 * 60 * 1000 } // 1 hour cache
    )
  }, [])

  const getStoredCategories = useCallback(() => {
    return dataCache.getFromLocalStorage<ServiceCategory[]>(
      "vanity_service_categories",
      ServiceStorage.getServiceCategories(),
      { expiryTimeMs: 60 * 60 * 1000 } // 1 hour cache
    )
  }, [])

  // Load services and categories on initial render - only once
  useEffect(() => {
    if (isInitialized) return;

    try {
      const storedServices = getStoredServices()
      const storedCategories = getStoredCategories()

      // Ensure we have valid arrays
      setServices(Array.isArray(storedServices) ? storedServices : ServiceStorage.getServices())
      setCategories(Array.isArray(storedCategories) ? storedCategories : ServiceStorage.getServiceCategories())
    } catch (error) {
      console.error("Error loading services or categories:", error)
      // Fallback to direct storage access
      setServices(ServiceStorage.getServices())
      setCategories(ServiceStorage.getServiceCategories())
    }

    setIsInitialized(true)
  }, [getStoredServices, getStoredCategories, isInitialized])

  // Refresh services from storage - with optimization to prevent unnecessary updates
  const refreshServices = useCallback(() => {
    try {
      const storedServices = getStoredServices()

      // Ensure we have a valid array
      const validServices = Array.isArray(storedServices) ? storedServices : ServiceStorage.getServices()

      // Only update if the services have changed
      if (JSON.stringify(validServices) !== JSON.stringify(services)) {
        setServices(validServices)
      }
    } catch (error) {
      console.error("Error refreshing services:", error)
      // Fallback to direct storage access
      setServices(ServiceStorage.getServices())
    }
  }, [getStoredServices, services])

  // Refresh categories from storage - with optimization to prevent unnecessary updates
  const refreshCategories = useCallback(() => {
    try {
      const storedCategories = getStoredCategories()

      // Ensure we have a valid array
      const validCategories = Array.isArray(storedCategories) ? storedCategories : ServiceStorage.getServiceCategories()

      // Only update if the categories have changed
      if (JSON.stringify(validCategories) !== JSON.stringify(categories)) {
        setCategories(validCategories)
      }
    } catch (error) {
      console.error("Error refreshing categories:", error)
      // Fallback to direct storage access
      setCategories(ServiceStorage.getServiceCategories())
    }
  }, [getStoredCategories, categories])

  // Get a service by ID
  const getServiceById = useCallback((id: string) => {
    if (!Array.isArray(services)) {
      return undefined;
    }
    return services.find(service => service.id === id)
  }, [services])

  // Get a category by ID
  const getCategoryById = useCallback((id: string) => {
    if (!Array.isArray(categories)) {
      return undefined;
    }
    return categories.find(category => category.id === id)
  }, [categories])

  // Get a category name by ID
  const getCategoryName = useCallback((id: string) => {
    if (!Array.isArray(categories)) {
      return "Uncategorized";
    }
    const category = categories.find(category => category.id === id)
    return category ? category.name : "Uncategorized"
  }, [categories])

  // Add a new service
  const addService = useCallback((serviceData: Omit<Service, "id">) => {
    console.log("Adding new service:", serviceData)

    // Validate service data
    if (!serviceData.name) {
      console.error("Cannot add service: Missing name")
      throw new Error("Service name is required")
    }

    // Ensure locations is an array
    if (!Array.isArray(serviceData.locations) || serviceData.locations.length === 0) {
      console.warn("Service locations is empty or not an array, fixing")
      serviceData.locations = Array.isArray(serviceData.locations) ? serviceData.locations : []
      if (serviceData.locations.length === 0) {
        console.warn("No locations specified, adding current location as default")
        serviceData.locations = ["loc1"] // Default to first location if none specified
      }
    }

    const newService: Service = {
      id: uuidv4(),
      ...serviceData
    }

    console.log("New service with ID:", newService)

    try {
      // Add service to storage
      ServiceStorage.addService(newService)
      console.log("Service added to storage")

      // Update state
      setServices(prev => {
        if (!Array.isArray(prev)) {
          console.warn("Previous services state is not an array, creating new array")
          return [newService]
        }
        return [...prev, newService]
      })

      // Update category service count
      if (newService.category) {
        console.log("Updating category count for:", newService.category)
        const category = categories.find(c => c.id === newService.category)
        console.log("Found category:", category)

        if (category) {
          const updatedCategory = {
            ...category,
            serviceCount: category.serviceCount + 1
          }
          ServiceStorage.updateServiceCategory(updatedCategory)
          setCategories(prev => {
            if (!Array.isArray(prev)) {
              console.warn("Previous categories state is not an array, creating new array")
              return [updatedCategory]
            }
            return prev.map(c => c.id === updatedCategory.id ? updatedCategory : c)
          })
          console.log("Category service count updated")
        } else {
          console.warn("Category not found:", newService.category)
        }
      } else {
        console.warn("No category specified for service")
      }

      console.log("Service added successfully:", newService.name)
      return newService
    } catch (error) {
      console.error("Error adding service:", error)
      throw error
    }
  }, [categories])

  // Update an existing service
  const updateService = useCallback((updatedService: Service) => {
    console.log("Service Provider: Updating service", updatedService.id, updatedService.name)

    if (!updatedService || !updatedService.id) {
      console.error("Cannot update service: Invalid service data", updatedService)
      return
    }

    // Ensure locations is an array
    if (!Array.isArray(updatedService.locations)) {
      console.warn("Service locations is not an array, fixing", updatedService.id)
      updatedService.locations = updatedService.locations ? [updatedService.locations as any] : []
    }

    // Get the original service to check if category changed
    const originalService = services.find(s => s.id === updatedService.id)

    if (!originalService) {
      console.warn("Original service not found in state, proceeding with update anyway", updatedService.id)
    }

    try {
      // Update service in storage
      ServiceStorage.updateService(updatedService)
      console.log("Service updated in storage", updatedService.id)

      // Update state
      setServices(prev => {
        if (!Array.isArray(prev)) {
          console.warn("Previous services state is not an array, creating new array")
          return [updatedService]
        }
        return prev.map(service => service.id === updatedService.id ? updatedService : service)
      })

      // Update category service counts if category changed
      if (originalService && originalService.category !== updatedService.category) {
        console.log("Category changed from", originalService.category, "to", updatedService.category)

        // Decrement old category count
        if (originalService.category) {
          const oldCategory = categories.find(c => c.id === originalService.category)
          if (oldCategory) {
            const updatedOldCategory = {
              ...oldCategory,
              serviceCount: Math.max(0, oldCategory.serviceCount - 1)
            }
            ServiceStorage.updateServiceCategory(updatedOldCategory)
            setCategories(prev => {
              if (!Array.isArray(prev)) {
                console.warn("Previous categories state is not an array, creating new array")
                return [updatedOldCategory]
              }
              return prev.map(c => c.id === updatedOldCategory.id ? updatedOldCategory : c)
            })
          }
        }

        // Increment new category count
        if (updatedService.category) {
          const newCategory = categories.find(c => c.id === updatedService.category)
          if (newCategory) {
            const updatedNewCategory = {
              ...newCategory,
              serviceCount: newCategory.serviceCount + 1
            }
            ServiceStorage.updateServiceCategory(updatedNewCategory)
            setCategories(prev => {
              if (!Array.isArray(prev)) {
                console.warn("Previous categories state is not an array, creating new array")
                return [updatedNewCategory]
              }
              return prev.map(c => c.id === updatedNewCategory.id ? updatedNewCategory : c)
            })
          }
        }
      }

      console.log("Service update completed successfully", updatedService.id)
    } catch (error) {
      console.error("Error updating service:", error)
    }
  }, [services, categories])

  // Delete a service
  const deleteService = useCallback((serviceId: string) => {
    console.log("Service Provider: Deleting service", serviceId)

    if (!serviceId) {
      console.error("Cannot delete service: Invalid service ID")
      return
    }

    try {
      if (!Array.isArray(services)) {
        console.warn("Services is not an array, initializing as empty array")
        // Continue with deletion from storage even if state is invalid
      }

      if (!Array.isArray(categories)) {
        console.warn("Categories is not an array, initializing as empty array")
        // Continue with deletion from storage even if state is invalid
      }

      // Get the service to be deleted
      const serviceToDelete = Array.isArray(services) ? services.find(s => s.id === serviceId) : null

      if (!serviceToDelete) {
        console.warn("Service to delete not found in state, proceeding with deletion from storage anyway", serviceId)
      }

      // Delete service from storage
      ServiceStorage.deleteService(serviceId)
      console.log("Service deleted from storage", serviceId)

      // Update state
      setServices(prev => {
        if (!Array.isArray(prev)) {
          console.warn("Previous services state is not an array, returning empty array")
          return []
        }
        return prev.filter(service => service.id !== serviceId)
      })

      // Update category service count
      if (serviceToDelete && serviceToDelete.category) {
        const category = Array.isArray(categories) ? categories.find(c => c.id === serviceToDelete.category) : null

        if (category) {
          const updatedCategory = {
            ...category,
            serviceCount: Math.max(0, category.serviceCount - 1)
          }

          ServiceStorage.updateServiceCategory(updatedCategory)

          setCategories(prev => {
            if (!Array.isArray(prev)) {
              console.warn("Previous categories state is not an array, creating new array")
              return [updatedCategory]
            }
            return prev.map(c => c.id === updatedCategory.id ? updatedCategory : c)
          })
        }
      }

      console.log("Service deletion completed successfully", serviceId)
    } catch (error) {
      console.error("Error deleting service:", error)
    }
  }, [services, categories])

  // Add a new category
  const addCategory = useCallback((categoryData: Omit<ServiceCategory, "id">) => {
    const newCategory: ServiceCategory = {
      id: uuidv4(),
      ...categoryData
    }

    // Add category to storage
    ServiceStorage.addServiceCategory(newCategory)

    // Update state
    setCategories(prev => [...prev, newCategory])

    return newCategory
  }, [])

  // Update an existing category
  const updateCategory = useCallback((updatedCategory: ServiceCategory) => {
    // Update category in storage
    ServiceStorage.updateServiceCategory(updatedCategory)

    // Update state
    setCategories(prev => prev.map(category =>
      category.id === updatedCategory.id ? updatedCategory : category
    ))
  }, [])

  // Delete a category
  const deleteCategory = useCallback((categoryId: string) => {
    if (!Array.isArray(categories)) {
      console.error("Cannot delete category: categories is not an array");
      return;
    }

    // Delete category from storage
    ServiceStorage.deleteServiceCategory(categoryId)

    // Update state
    setCategories(prev => Array.isArray(prev) ? prev.filter(category => category.id !== categoryId) : [])

    // Update services that used this category
    if (Array.isArray(services)) {
      const servicesWithCategory = services.filter(s => s.category === categoryId)
      if (servicesWithCategory.length > 0) {
        const updatedServices = servicesWithCategory.map(service => ({
          ...service,
          category: "" // Set to empty/uncategorized
        }))

        // Update each service
        updatedServices.forEach(service => {
          ServiceStorage.updateService(service)
        })

        // Update state
        setServices(prev => Array.isArray(prev) ?
          prev.map(service => service.category === categoryId ? { ...service, category: "" } : service) :
          updatedServices
        )
      }
    }
  }, [services, categories])

  return (
    <ServiceContext.Provider
      value={{
        services,
        categories,
        getServiceById,
        getCategoryById,
        getCategoryName,
        refreshServices,
        refreshCategories,
        addService,
        updateService,
        deleteService,
        addCategory,
        updateCategory,
        deleteCategory
      }}
    >
      {children}
    </ServiceContext.Provider>
  )
}

export const useServices = () => useContext(ServiceContext)
